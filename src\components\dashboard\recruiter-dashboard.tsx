
import {
  User<PERSON><PERSON><PERSON>,
  Target,
  Calendar,
  Users,
  Clock,
  FileText,
  Users2,
  <PERSON><PERSON><PERSON><PERSON>,
  ArrowR<PERSON>,
  TrendingUp,
  Check,
  ChevronDown
} from "lucide-react";
import React, { useState, useEffect, useRef } from "react";
import { RecruiterTargets } from "../modals/target-management-modal";
import { useUser } from "@/contexts/user-context";
import { useDataManagement } from "@/hooks/use-data-management";
import { type Candidate } from "@/types/candidate";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { selectJobs } from "@/store/selectors/jobsSelectors";
import { fetchJobs } from "@/store/slices/jobsSlice";
import { ViewAllPipelineCandidatesModal } from "../modals/view-all-pipeline-candidates-modal";
// KPI Card Component
interface KPICardProps {
  title: string;
  currentValue: number;
  targetRange: string;
  color: string;
  icon: React.ComponentType<any>;
}

function KPICard({
  title,
  currentValue,
  targetRange,
  color,
  icon: Icon,
}: KPICardProps) {
  return (
    <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-gray-600">{title}</h3>
        <Icon className={`w-5 h-5 ${color}`} />
      </div>
      <div className="text-3xl font-bold text-gray-900 mb-1">
        {currentValue}
      </div>
      <div className="text-sm text-gray-500">Target: {targetRange}</div>
    </div>
  );
}

// Pipeline Stage Component
interface PipelineStageProps {
  title: string;
  candidates: string[];
  count: number;
  color: string;
}

function PipelineStage({
  title,
  candidates,
  count,
  color,
}: PipelineStageProps) {
  return (
    <div className="pipeline-column px-2 py-2 bg-white rounded-2xl shadow-md flex-shrink-0 w-[250px] mr-4">
      <h2 className="text-lg font-semibold text-gray-700 flex items-center justify-between mb-1 border-b pb-1">
        <span className="capitalize">{title}</span>
        <span className={`px-2 py-1 text-xs font-semibold rounded-full text-white ${color}`}>
          {count}
        </span>
      </h2>
      <div className="space-y-4 min-h-[50px] overflow-y-auto pr-1 ">
        {candidates && candidates.length > 0 ? (
          candidates.map(candidate => (
            <div className="bg-gray-50 p-2 rounded-xl cursor-pointer hover:bg-white border border-gray-100 transition-colors">
              <p className="font-medium text-gray-800">{candidate}</p>
            </div>
          ))
        ) : (
          <p className="text-sm text-gray-400 text-center py-4">No candidates in this stage.</p>
        )}
      </div>
    </div>
  );
}

// To-Do Item Component
interface TodoItemProps {
  text: string;
  icon: React.ComponentType<any>;
  iconColor: string;
}

function TodoItem({ text, icon: Icon, iconColor }: TodoItemProps) {
  return (
    <div className="flex items-center space-x-3 p-3 bg-white rounded-lg shadow-sm border border-gray-200">
      <Icon className={`w-5 h-5 ${iconColor}`} />
      <span className="text-sm text-gray-700">{text}</span>
    </div>
  );
}

export function RecruiterDashboard() {
  const { userName } = useUser();
  const dispatch = useAppDispatch();

  // Get current user info from localStorage
  const currentUserId = localStorage.getItem('userId');

  // Use data management hook for Redux store access
  const {
    candidates,
    candidatesLoading: loading,
    candidatesError: error,
    refreshCandidates
  } = useDataManagement();

  // Get jobs data from Redux store
  const jobs = useAppSelector(selectJobs);

  // Get user's full name from localStorage (stored during login)
  const userFullName = localStorage.getItem('name') || '';

  // Get targets from localStorage
  const [targets, setTargets] = useState<RecruiterTargets | null>(null);

  // Modal state for viewing all pipeline candidates
  const [isViewAllModalOpen, setIsViewAllModalOpen] = useState(false);
  const [selectedPipelineStage, setSelectedPipelineStage] = useState<string>("");
  const [selectedPipelineCandidates, setSelectedPipelineCandidates] = useState<Candidate[]>([]);

  // Date filter state
  const [dateFilter, setDateFilter] = useState<number | null>(null);
  const [isDateDropdownOpen, setIsDateDropdownOpen] = useState(false);
  const [customStartDate, setCustomStartDate] = useState<string>("");
  const [customEndDate, setCustomEndDate] = useState<string>("");
  const [appliedCustomDateRange, setAppliedCustomDateRange] = useState<{
    start: string;
    end: string;
  } | null>(null);

  // Refs for date dropdown
  const dateDropdownRef = useRef<HTMLDivElement>(null);
  const dateButtonRef = useRef<HTMLButtonElement>(null);

  // Fetch candidates data on component mount using Redux
  useEffect(() => {
    if (userName && currentUserId) {
      refreshCandidates();
      dispatch(fetchJobs({ username: userName }));
    }
  }, [userName, currentUserId, refreshCandidates, dispatch]);

  useEffect(() => {
    // Load targets from localStorage
    const savedTargets = localStorage.getItem(`recruiterTargets_${currentUserId}`);
    if (savedTargets) {
      const parsedTargets = JSON.parse(savedTargets);
      setTargets(parsedTargets);
    }
  }, [currentUserId]);



  // Get current targets
  const currentTargets = targets;

  // Define the date filter options
  const dateFilterOptions = [
    { value: null, label: "All Time" },
    { value: 1, label: "Last 24 Hours" },
    { value: 7, label: "Last 7 Days" },
    { value: 30, label: "Last 30 Days" },
  ];

  // Add effect to close date dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dateDropdownRef.current &&
        !dateDropdownRef.current.contains(event.target as Node) &&
        dateButtonRef.current &&
        !dateButtonRef.current.contains(event.target as Node)
      ) {
        setIsDateDropdownOpen(false);
      }
    }

    if (isDateDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [isDateDropdownOpen]);

  const handleDateFilter = (days: number | null) => {
    setDateFilter(days);
    setIsDateDropdownOpen(false);
    setCustomStartDate("");
    setCustomEndDate("");
    setAppliedCustomDateRange(null);
  };

  // Get the current date filter label for display
  const getCurrentDateFilterLabel = () => {
    if (appliedCustomDateRange) {
      const startDate = new Date(
        appliedCustomDateRange.start
      ).toLocaleDateString();
      const endDate = new Date(appliedCustomDateRange.end).toLocaleDateString();
      return `${startDate} - ${endDate}`;
    }
    const option = dateFilterOptions.find((opt) => opt.value === dateFilter);
    return option ? option.label : "Select Date";
  };

  const handleCustomDateApply = () => {
    if (customStartDate && customEndDate) {
      setAppliedCustomDateRange({
        start: customStartDate,
        end: customEndDate,
      });
      setDateFilter(null);
      setIsDateDropdownOpen(false);
    } else {
      alert("Please select both start and end dates");
    }
  };

  // Filter candidates based on selected date range
  const getFilteredCandidates = () => {
    if (!dateFilter && !appliedCustomDateRange) {
      return candidates;
    }

    const now = new Date();
    let startDate: Date;

    if (dateFilter) {
      // Calculate start date based on filter
      startDate = new Date(now.getTime() - (dateFilter * 24 * 60 * 60 * 1000));
    } else if (appliedCustomDateRange) {
      // Use custom date range
      startDate = new Date(appliedCustomDateRange.start);
      const endDate = new Date(appliedCustomDateRange.end);

      return candidates.filter(candidate => {
        const candidateDate = new Date(candidate.appliedDate);
        return candidateDate >= startDate && candidateDate <= endDate;
      });
    }

    // Filter by date filter
    return candidates.filter(candidate => {
      const candidateDate = new Date(candidate.appliedDate);
      return candidateDate >= startDate;
    });
  };

  // Get filtered candidates based on date selection
  const filteredCandidates = getFilteredCandidates();

  // Calculate real metrics from candidate data
  const calculateMetrics = (candidates: Candidate[], jobs: any[]) => {
    const metrics = {
      profilesSubmitted: 0,
      interviewsScheduled: 0,
      offers: 0,
      joiners: 0,
      requirementsAssigned: 0,
    };

    candidates.forEach(candidate => {
      const status = candidate.status.toLowerCase();

      // Count profiles submitted (candidates with any status)
      metrics.profilesSubmitted++;

      // Count interviews scheduled
      if (status.includes('interview') || status.includes('scheduled') || status.includes('shortlisted')) {
        metrics.interviewsScheduled++;
      }

      // Count offers
      if (status.includes('offer') || status.includes('placed') || status.includes('selected')) {
        metrics.offers++;
      }

      // Count joiners (candidates who joined)
      if (status.includes('joined') || status.includes('onboarded') || status.includes('hired')) {
        metrics.joiners++;
      }
    });

    // Count active requirements/jobs assigned to this recruiter

    console.log(jobs, "jobs")
    console.log("Current userFullName from localStorage:", userFullName)
    const activeJobs = jobs.filter(job => {
      // Check if job is active
      if (job.job_status !== "Active") return false;

      // Handle multiple recruiters separated by commas
      const assignedRecruiters = job.recruiter.split(',').map((r: string) => r.trim());
      console.log(`Job ${job.id} recruiters:`, assignedRecruiters, "userName:", userName)
      // We need to match either by username or by name
      const isAssignedToUser = assignedRecruiters.some((recruiter: string) => {
        // Try to match by username first
        if (recruiter === userName) return true;

        // Try to match by full name from localStorage
        if (recruiter === userFullName) return true;

        return false;
      });

      console.log(`Job ${job.id} isAssignedToUser:`, isAssignedToUser)
      return isAssignedToUser;
    });
    metrics.requirementsAssigned = activeJobs.length;

    return metrics;
  };

  const dashboardData = calculateMetrics(filteredCandidates, jobs);

  // Calculate pipeline data from real candidate data
  const calculatePipelineData = (candidates: Candidate[]) => {
    const pipeline = {
      sourced: { count: 0, candidates: [] as string[] },
      screened: { count: 0, candidates: [] as string[] },
      submitted: { count: 0, candidates: [] as string[] },
      interview: { count: 0, candidates: [] as string[] },
      offer: { count: 0, candidates: [] as string[] },
      joined: { count: 0, candidates: [] as string[] },
    };

    candidates.forEach(candidate => {
      const status = candidate.status.toLowerCase();
      const candidateName = candidate.firstName || `Candidate ${candidate.id}`;

      if (status.includes('sourced') || status.includes('initial') || status.includes('new')) {
        pipeline.sourced.count++;
        if (pipeline.sourced.candidates.length < 3) {
          pipeline.sourced.candidates.push(candidateName);
        }
      } else if (status.includes('screened') || status.includes('shortlisted') || status.includes('pending')) {
        pipeline.screened.count++;
        if (pipeline.screened.candidates.length < 3) {
          pipeline.screened.candidates.push(candidateName);
        }
      } else if (status.includes('submitted') || status.includes('profile') || status.includes('active')) {
        pipeline.submitted.count++;
        if (pipeline.submitted.candidates.length < 3) {
          pipeline.submitted.candidates.push(candidateName);
        }
      } else if (status.includes('interview') || status.includes('scheduled') || status.includes('shortlisted')) {
        pipeline.interview.count++;
        if (pipeline.interview.candidates.length < 3) {
          pipeline.interview.candidates.push(candidateName);
        }
      } else if (status.includes('offer') || status.includes('placed') || status.includes('selected')) {
        pipeline.offer.count++;
        if (pipeline.offer.candidates.length < 3) {
          pipeline.offer.candidates.push(candidateName);
        }
      } else if (status.includes('joined') || status.includes('onboarded') || status.includes('hired')) {
        pipeline.joined.count++;
        if (pipeline.joined.candidates.length < 3) {
          pipeline.joined.candidates.push(candidateName);
        }
      }
    });

    return pipeline;
  };

  const pipelineData = calculatePipelineData(filteredCandidates);

  const todoItems = [
    { text: "Follow-up with Candidate A", icon: Clock, color: "text-blue-500" },
    {
      text: "Submit profile to Manager",
      icon: FileText,
      color: "text-green-500",
    },
    {
      text: "Schedule interview for Candidate B",
      icon: Users2,
      color: "text-purple-500",
    },
  ];

  // Handle viewing all candidates across all pipeline stages
  const handleViewAllPipeline = () => {
    setSelectedPipelineStage("All Pipeline Stages");
    setSelectedPipelineCandidates(filteredCandidates);
    setIsViewAllModalOpen(true);
  };

  // Show loading state
  if (loading) {
    return (
      <div className="flex-1 p-6 bg-gray-50 overflow-y-auto h-full">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading dashboard data...</p>
              <p className="text-sm text-gray-500 mt-2">Fetching candidates from Redux store</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex-1 p-6 bg-gray-50 overflow-y-auto h-full">
        <div className="max-w-7xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error loading dashboard data</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 p-6 bg-gray-50 overflow-y-auto h-full">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">Recruiter Dashboard</h1>

            {/* Date Filter */}
            <div className="relative">
              <button
                ref={dateButtonRef}
                onClick={() => setIsDateDropdownOpen(!isDateDropdownOpen)}
                className="bg-white border border-gray-300 text-gray-900 px-3 py-2 rounded-md text-sm font-medium flex items-center gap-2 hover:bg-gray-50 focus:ring-blue-500 focus:border-blue-500"
              >
                <Calendar className="h-4 w-4 text-gray-500" />
                <span>{getCurrentDateFilterLabel()}</span>
                <ChevronDown
                  className={`h-4 w-4 transition-transform ${isDateDropdownOpen ? "rotate-180" : ""}`}
                />
              </button>

              {isDateDropdownOpen && (
                <div
                  ref={dateDropdownRef}
                  className="absolute right-0 top-full mt-1 z-[9999] bg-white rounded-lg shadow-xl border border-gray-200 min-w-[280px] py-2 animate-in fade-in-0 zoom-in-95 duration-200"
                >
                  <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-b border-gray-200">
                    Filter by Date
                  </div>
                  {dateFilterOptions.map((option) => (
                    <button
                      key={option.value || "all"}
                      onClick={() => handleDateFilter(option.value)}
                      className={`w-full text-left px-3 py-2 hover:bg-gray-50 text-sm transition-colors duration-150 flex items-center justify-between ${dateFilter === option.value
                        ? "bg-blue-50 text-blue-700 border-r-2 border-r-blue-500"
                        : "text-gray-700"
                        }`}
                    >
                      <span>{option.label}</span>
                      {dateFilter === option.value && (
                        <Check className="h-4 w-4 text-blue-600" />
                      )}
                    </button>
                  ))}

                  {/* Custom Date Range Section */}
                  <div className="border-t border-gray-200 p-3 bg-gray-50">
                    <div className="space-y-3">
                      <div className="text-xs font-semibold text-gray-600 mb-2">
                        Or Select Custom Date Range
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <label className="block text-xs text-gray-500 mb-1">
                            Start Date
                          </label>
                          <input
                            type="date"
                            className="w-full border border-gray-300 px-2 py-1 rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            value={customStartDate}
                            onChange={(e) => setCustomStartDate(e.target.value)}
                          />
                        </div>
                        <div>
                          <label className="block text-xs text-gray-500 mb-1">
                            End Date
                          </label>
                          <input
                            type="date"
                            className="w-full border border-gray-300 px-2 py-1 rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            value={customEndDate}
                            onChange={(e) => setCustomEndDate(e.target.value)}
                          />
                        </div>
                      </div>
                      <div className="flex gap-2 justify-end">
                        <button
                          onClick={() => {
                            setCustomStartDate("");
                            setCustomEndDate("");
                            if (!dateFilter) {
                              setIsDateDropdownOpen(false);
                            }
                          }}
                          className="px-3 py-1 text-xs text-gray-600 hover:text-gray-800 border border-gray-300 rounded hover:bg-gray-100"
                        >
                          Clear
                        </button>
                        <button
                          onClick={handleCustomDateApply}
                          className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                          disabled={!customStartDate || !customEndDate}
                        >
                          Apply
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>



        {/* KPI Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-6">
          <KPICard
            title="Profiles Submitted"
            currentValue={dashboardData.profilesSubmitted}
            targetRange={`${currentTargets?.targets.profilesSubmitted.min}-${currentTargets?.targets.profilesSubmitted.max}`}
            color="text-blue-500"
            icon={Users}
          />
          <KPICard
            title="Interviews Scheduled"
            currentValue={dashboardData.interviewsScheduled}
            targetRange={`${currentTargets?.targets.interviewsScheduled.min}-${currentTargets?.targets.interviewsScheduled.max}`}
            color="text-green-500"
            icon={Calendar}
          />
          <KPICard
            title="Offers"
            currentValue={dashboardData.offers}
            targetRange={`${currentTargets?.targets.offers.min}-${currentTargets?.targets.offers.max}`}
            color="text-purple-500"
            icon={CheckCircle}
          />
          <KPICard
            title="Joiners"
            currentValue={dashboardData.joiners}
            targetRange={`${currentTargets?.targets.joiners.min}-${currentTargets?.targets.joiners.max}`}
            color="text-red-500"
            icon={UserCheck}
          />
          <KPICard
            title="Requirements Assigned"
            currentValue={dashboardData.requirementsAssigned}
            targetRange={`${currentTargets?.targets.requirementsAssigned.min}-${currentTargets?.targets.requirementsAssigned.max} ${currentTargets?.targets.requirementsAssigned.unit}`}
            color="text-green-600"
            icon={Target}
          />
        </div>



        {/* Target Status Indicator */}
        {targets && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Target Performance</h3>
              <span className="text-sm text-gray-500">Current vs. Monthly Targets</span>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              {/* Profiles Submitted Status */}
              <div className="text-center">
                <div className={`text-2xl font-bold ${dashboardData.profilesSubmitted >= (currentTargets?.targets.profilesSubmitted.min || 0)
                  ? 'text-green-600'
                  : 'text-red-600'
                  }`}>
                  {dashboardData.profilesSubmitted}
                </div>
                <div className="text-xs text-gray-500">of {currentTargets?.targets.profilesSubmitted.min || 0}-{currentTargets?.targets.profilesSubmitted.max || 0}</div>
                <div className="text-xs font-medium text-gray-700">Profiles</div>
              </div>

              {/* Interviews Status */}
              <div className="text-center">
                <div className={`text-2xl font-bold ${dashboardData.interviewsScheduled >= (currentTargets?.targets.interviewsScheduled.min || 0)
                  ? 'text-green-600'
                  : 'text-red-600'
                  }`}>
                  {dashboardData.interviewsScheduled}
                </div>
                <div className="text-xs text-gray-500">of {currentTargets?.targets.interviewsScheduled.min || 0}-{currentTargets?.targets.interviewsScheduled.max || 0}</div>
                <div className="text-xs font-medium text-gray-700">Interviews</div>
              </div>

              {/* Offers Status */}
              <div className="text-center">
                <div className={`text-2xl font-bold ${dashboardData.offers >= (currentTargets?.targets.offers.min || 0)
                  ? 'text-green-600'
                  : 'text-red-600'
                  }`}>
                  {dashboardData.offers}
                </div>
                <div className="text-xs text-gray-500">of {currentTargets?.targets.offers.min || 0}-{currentTargets?.targets.offers.max || 0}</div>
                <div className="text-xs font-medium text-gray-700">Offers</div>
              </div>

              {/* Joiners Status */}
              <div className="text-center">
                <div className={`text-2xl font-bold ${dashboardData.joiners >= (currentTargets?.targets.joiners.min || 0)
                  ? 'text-green-600'
                  : 'text-red-600'
                  }`}>
                  {dashboardData.joiners}
                </div>
                <div className="text-xs text-gray-500">of {currentTargets?.targets.joiners.min || 0}-{currentTargets?.targets.joiners.max || 0}</div>
                <div className="text-xs font-medium text-gray-700">Joiners</div>
              </div>

              {/* Requirements Status */}
              <div className="text-center">
                <div className={`text-2xl font-bold ${dashboardData.requirementsAssigned >= (currentTargets?.targets.requirementsAssigned.min || 0)
                  ? 'text-green-600'
                  : 'text-red-600'
                  }`}>
                  {dashboardData.requirementsAssigned}
                </div>
                <div className="text-xs text-gray-500">of {currentTargets?.targets.requirementsAssigned.min || 0}-{currentTargets?.targets.requirementsAssigned.max || 0}</div>
                <div className="text-xs font-medium text-gray-700">Requirements</div>
              </div>
            </div>
          </div>
        )}



        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Candidate Pipeline - Takes 2 columns */}
          <div className="lg:col-span-2 ">
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">
                  Candidate Pipeline
                </h2>
                <button
                  onClick={handleViewAllPipeline}
                  className="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center"
                >
                  View All
                  <ArrowRight className="w-4 h-4 ml-1" />
                </button>
              </div>

              <div className="flex space-x-4 overflow-x-auto pb-4">
                <PipelineStage
                  title="Sourced"
                  candidates={pipelineData.sourced.candidates}
                  count={pipelineData.sourced.count}
                  color="bg-blue-500"
                />
                <PipelineStage
                  title="Screened"
                  candidates={pipelineData.screened.candidates}
                  count={pipelineData.screened.count}
                  color="bg-yellow-500"
                />
                <PipelineStage
                  title="Submitted"
                  candidates={pipelineData.submitted.candidates}
                  count={pipelineData.submitted.count}
                  color="bg-purple-500"
                />
                <PipelineStage
                  title="Interview"
                  candidates={pipelineData.interview.candidates}
                  count={pipelineData.interview.count}
                  color="bg-indigo-500"
                />
                <PipelineStage
                  title="Offer"
                  candidates={pipelineData.offer.candidates}
                  count={pipelineData.offer.count}
                  color="bg-orange-500"
                />
                <PipelineStage
                  title="Joined"
                  candidates={pipelineData.joined.candidates}
                  count={pipelineData.joined.count}
                  color="bg-green-500"
                />
              </div>
            </div>
          </div>

          {/* To-Do & Reminders - Takes 1 column */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">
                To-Do & Reminders
              </h2>
              <div className="space-y-6">
                {todoItems.map((item, index) => (
                  <TodoItem
                    key={index}
                    text={item.text}
                    icon={item.icon}
                    iconColor={item.color}
                  />
                ))}
              </div>
              {/* <button className="w-full mt-6 text-blue-600 hover:text-blue-700 text-sm font-medium">
                View All Tasks
              </button> */}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8 mt-5">
          {/* One-Time Hiring Incentive */}
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg shadow-md p-6 border border-green-200">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-green-800">One-Time Hiring Incentive</h3>
                <p className="text-sm text-green-600 mt-1">Contract & Project-based Placements</p>
              </div>
              <div className="p-3 rounded-full bg-green-500">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
            </div>

            <div className="space-y-3">
              <div className="bg-white rounded-lg p-4 border border-green-100">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-700">Incentive Amount</span>
                  <span className="text-2xl font-bold text-green-600">₹15,000</span>
                </div>
                {/* <p className="text-xs text-gray-500 mt-1">Per successful placement</p> */}
              </div>
            </div>
          </div>

          {/* Full-Time Hiring Incentive */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg shadow-md p-6 border border-blue-200">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-blue-800">Full-Time Hiring Incentive</h3>
                <p className="text-sm text-blue-600 mt-1">Permanent & Long-term Placements</p>
              </div>
              <div className="p-3 rounded-full bg-blue-500">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>

            <div className="space-y-3">
              <div className="bg-white rounded-lg p-4 border border-blue-100">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-700">Incentive Amount</span>
                  <span className="text-2xl font-bold text-blue-600">₹25,000</span>
                </div>
                {/* <p className="text-xs text-gray-500 mt-1">Per successful placement</p> */}
              </div>
            </div>
          </div>
        </div>

        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
          <div className="flex items-start space-x-3">
            <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-white text-sm font-bold">!</span>
            </div>
            <div>
              <h4 className="text-sm font-semibold text-red-800 mb-2">Important Notes</h4>
              <div className="space-y-2 text-sm text-red-700">
                <div className="flex items-start space-x-2">
                  <span className="text-red-500 font-bold">•</span>
                  <span><strong>Incentive is NOT applicable</strong> for hiring Freshers / Trainees</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-red-500 font-bold">•</span>
                  <span><strong>Incentive is NOT applicable</strong> for non-billable hiring</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-red-500 font-bold">•</span>
                  <span>All placements must be verified and approved by management</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-red-500 font-bold">•</span>
                  <span>Incentive is paid after successful completion of 3 Month period</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* View All Pipeline Candidates Modal */}
      <ViewAllPipelineCandidatesModal
        isOpen={isViewAllModalOpen}
        onClose={() => setIsViewAllModalOpen(false)}
        candidates={selectedPipelineCandidates}
        pipelineStage={selectedPipelineStage}
      />
    </div>
  );
}
