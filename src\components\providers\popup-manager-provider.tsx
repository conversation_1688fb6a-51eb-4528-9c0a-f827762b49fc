import React, { useState } from 'react';
import { PopupManagerContext } from '@/hooks/use-popup-manager';

interface PopupManagerProviderProps {
    children: React.ReactNode;
}

export const PopupManagerProvider: React.FC<PopupManagerProviderProps> = ({ children }) => {
    const [openPopupId, setOpenPopupId] = useState<string | null>(null);

    return (
        <PopupManagerContext.Provider value={{ openPopupId, setOpenPopupId }}>
            {children}
        </PopupManagerContext.Provider>
    );
};
