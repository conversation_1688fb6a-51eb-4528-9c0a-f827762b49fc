import { useState, useEffect } from 'react';
import { X, Target, User, Save, Edit3 } from 'lucide-react';
import { toast } from 'react-toastify';

export interface RecruiterTargets {
  recruiterId: string;
  recruiterName: string;
  timePeriod: 'weekly' | 'monthly';
  targets: {
    profilesSubmitted: { min: number; max: number };
    interviewsScheduled: { min: number; max: number };
    offers: { min: number; max: number };
    joiners: { min: number; max: number };
    requirementsAssigned: { min: number; max: number; unit: string };
  };
  lastUpdated: string;
}

interface TargetManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  recruiter: { id: string; name: string } | null;
  onSave: (targets: RecruiterTargets) => void;
  existingTargets?: RecruiterTargets;
}

export function TargetManagementModal({
  isOpen,
  onClose,
  recruiter,
  onSave,
  existingTargets
}: TargetManagementModalProps) {
  const [timePeriod, setTimePeriod] = useState<'weekly' | 'monthly'>('monthly');
  const [targets, setTargets] = useState({
    profilesSubmitted: { min: 20, max: 25 },
    interviewsScheduled: { min: 4, max: 6 },
    offers: { min: 2, max: 4 },
    joiners: { min: 1, max: 2 },
    requirementsAssigned: { min: 2, max: 3, unit: '/ day' }
  });

  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    if (existingTargets) {
      setTimePeriod(existingTargets.timePeriod);
      setTargets(existingTargets.targets);
    }
  }, [existingTargets]);

  const handleInputChange = (field: string, type: 'min' | 'max', value: string) => {
    const numValue = parseInt(value) || 0;
    setTargets(prev => ({
      ...prev,
      [field]: {
        ...prev[field as keyof typeof prev],
        [type]: numValue
      }
    }));
  };

  const handleUnitChange = (field: string, value: string) => {
    setTargets(prev => ({
      ...prev,
      [field]: {
        ...prev[field as keyof typeof prev],
        unit: value
      }
    }));
  };

  const handleSave = () => {
    if (!recruiter) return;

    // Validate targets
    const hasInvalidTargets = Object.values(targets).some(target => {
      if ('unit' in target) {
        return target.min > target.max;
      }
      return target.min > target.max;
    });

    if (hasInvalidTargets) {
      toast.error('Invalid targets: Minimum values cannot be greater than maximum values');
      return;
    }

    const recruiterTargets: RecruiterTargets = {
      recruiterId: recruiter.id,
      recruiterName: recruiter.name,
      timePeriod: timePeriod, // Use the selected timePeriod
      targets,
      lastUpdated: new Date().toISOString()
    };

    onSave(recruiterTargets);
    toast.success(`Targets updated for ${recruiter.name}`);
    onClose();
  };

  if (!isOpen || !recruiter) return null;

  return (
    <div className="fixed inset-0 bg-transparent backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Target className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Set Targets for Recruiter</h2>
              <p className="text-sm text-gray-600">{recruiter.name}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Time Period Selector */}
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <label className="text-sm font-medium text-gray-700 flex items-center">
                <Target className="h-4 w-4 mr-2 text-blue-600" />
                Time Period
              </label>
            </div>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="timePeriod"
                  value="weekly"
                  checked={timePeriod === 'weekly'}
                  onChange={(e) => setTimePeriod(e.target.value as 'weekly' | 'monthly')}
                  className="mr-2 text-blue-600 focus:ring-blue-500"
                  disabled={!isEditing}
                />
                <span className="text-sm text-gray-700">Weekly</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="timePeriod"
                  value="monthly"
                  checked={timePeriod === 'monthly'}
                  onChange={(e) => setTimePeriod(e.target.value as 'weekly' | 'monthly')}
                  className="mr-2 text-blue-600 focus:ring-blue-500"
                  disabled={!isEditing}
                />
                <span className="text-sm text-gray-700">Monthly</span>
              </label>
            </div>
          </div>

          {/* Target Fields */}
          <div className="space-y-4">
            {/* Profiles Submitted */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <label className="text-sm font-medium text-gray-700 flex items-center">
                  <User className="h-4 w-4 mr-2 text-blue-500" />
                  Profiles Submitted
                </label>
                <span className="text-xs text-gray-500">{timePeriod === 'weekly' ? 'Weekly' : 'Monthly'} Target</span>
              </div>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Min</label>
                  <input
                    type="number"
                    value={targets.profilesSubmitted.min}
                    onChange={(e) => handleInputChange('profilesSubmitted', 'min', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={!isEditing}
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Max</label>
                  <input
                    type="number"
                    value={targets.profilesSubmitted.max}
                    onChange={(e) => handleInputChange('profilesSubmitted', 'max', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={!isEditing}
                  />
                </div>
              </div>
            </div>

            {/* Interviews Scheduled */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <label className="text-sm font-medium text-gray-700 flex items-center">
                  <Target className="h-4 w-4 mr-2 text-green-500" />
                  Interviews Scheduled
                </label>
                <span className="text-xs text-gray-500">{timePeriod === 'weekly' ? 'Weekly' : 'Monthly'} Target</span>
              </div>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Min</label>
                  <input
                    type="number"
                    value={targets.interviewsScheduled.min}
                    onChange={(e) => handleInputChange('interviewsScheduled', 'min', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={!isEditing}
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Max</label>
                  <input
                    type="number"
                    value={targets.interviewsScheduled.max}
                    onChange={(e) => handleInputChange('interviewsScheduled', 'max', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={!isEditing}
                  />
                </div>
              </div>
            </div>

            {/* Offers */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <label className="text-sm font-medium text-gray-700 flex items-center">
                  <Target className="h-4 w-4 mr-2 text-purple-500" />
                  Offers
                </label>
                <span className="text-xs text-gray-500">{timePeriod === 'weekly' ? 'Weekly' : 'Monthly'} Target</span>
              </div>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Min</label>
                  <input
                    type="number"
                    value={targets.offers.min}
                    onChange={(e) => handleInputChange('offers', 'min', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={!isEditing}
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Max</label>
                  <input
                    type="number"
                    value={targets.offers.max}
                    onChange={(e) => handleInputChange('offers', 'max', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={!isEditing}
                  />
                </div>
              </div>
            </div>

            {/* Joiners */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <label className="text-sm font-medium text-gray-700 flex items-center">
                  <Target className="h-4 w-4 mr-2 text-red-500" />
                  Joiners
                </label>
                <span className="text-xs text-gray-500">{timePeriod === 'weekly' ? 'Weekly' : 'Monthly'} Target</span>
              </div>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Min</label>
                  <input
                    type="number"
                    value={targets.joiners.min}
                    onChange={(e) => handleInputChange('joiners', 'min', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={!isEditing}
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Max</label>
                  <input
                    type="number"
                    value={targets.joiners.max}
                    onChange={(e) => handleInputChange('joiners', 'max', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={!isEditing}
                  />
                </div>
              </div>
            </div>

            {/* Requirements Assigned */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <label className="text-sm font-medium text-gray-700 flex items-center">
                  <Target className="h-4 w-4 mr-2 text-green-600" />
                  Requirements Assigned
                </label>
                <span className="text-xs text-gray-500">Daily Target</span>
              </div>
              <div className="grid grid-cols-3 gap-3">
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Min</label>
                  <input
                    type="number"
                    value={targets.requirementsAssigned.min}
                    onChange={(e) => handleInputChange('requirementsAssigned', 'min', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={!isEditing}
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Max</label>
                  <input
                    type="number"
                    value={targets.requirementsAssigned.max}
                    onChange={(e) => handleInputChange('requirementsAssigned', 'max', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={!isEditing}
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Unit</label>
                  <select
                    value={targets.requirementsAssigned.unit}
                    onChange={(e) => handleUnitChange('requirementsAssigned', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={!isEditing}
                  >
                    <option value="/ day">/ day</option>
                    <option value="/ week">/ week</option>
                    <option value="/ month">/ month</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* Last Updated Info */}
          {existingTargets && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <p className="text-xs text-blue-700">
                Last updated: {new Date(existingTargets.lastUpdated).toLocaleDateString()} at{' '}
                {new Date(existingTargets.lastUpdated).toLocaleTimeString()}
              </p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <div className="flex space-x-3">
            {!isEditing ? (
              <button
                onClick={() => setIsEditing(true)}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <Edit3 className="h-4 w-4 mr-2" />
                Edit Targets
              </button>
            ) : (
              <button
                onClick={() => setIsEditing(false)}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
            )}
          </div>

          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
            >
              Close
            </button>
            {isEditing && (
              <button
                onClick={handleSave}
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
              >
                <Save className="h-4 w-4 mr-2" />
                Save Targets
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
