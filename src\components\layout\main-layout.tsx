import { SidebarProvider } from "@/components/ui/sidebar";
import { ManagerSidebar } from "@/components/sidebar/manager-sidebar";
import { RecruiterSidebar } from "@/components/sidebar/recruiter-sidebar";
import { Header } from "./header";
import { useUser } from "@/contexts/user-context";

interface MainLayoutProps {
  children: React.ReactNode;
}

export function MainLayout({ children }: MainLayoutProps) {
  const { userRole } = useUser();

  // Render the appropriate sidebar based on user role
  const renderSidebar = () => {
    switch (userRole) {
      case "manager":
        return <ManagerSidebar />;
      case "recruiter":
        return <RecruiterSidebar />;
      default:
        return <RecruiterSidebar />;
    }
  };

  return (
    <SidebarProvider>
      <div className="flex h-screen w-full overflow-hidden">
        {renderSidebar()}
        <div className="flex-1 w-full overflow-hidden flex flex-col bg-gray-50">
          <Header />
          <main className="flex-1 w-full overflow-hidden">{children}</main>
        </div>
      </div>
    </SidebarProvider>
  );
}
