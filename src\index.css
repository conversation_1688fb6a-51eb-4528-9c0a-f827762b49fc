@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

/* Custom scrollbar styling for all browsers */
* {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

/* Webkit scrollbar styling (Chrome, Safari, Edge) */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

::-webkit-scrollbar-thumb:active {
  background: #64748b;
}

::-webkit-scrollbar-corner {
  background: #f1f5f9;
}

/* Custom scrollbar for tables */
.table-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.table-scrollbar::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.table-scrollbar::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 2px;
}

.table-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.table-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Custom scrollbar for sidebar */
.sidebar-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.sidebar-scrollbar::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.sidebar-scrollbar::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 2px;
}

.sidebar-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.sidebar-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Custom scrollbar for modals and dialogs */
.modal-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.modal-scrollbar::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

/* Table column width constraints */
.table-fixed-layout {
  table-layout: fixed;
}

.table-auto-layout {
  table-layout: auto;
}

/* Skills column specific styling */
.skills-column {
  max-width: 200px;
  min-width: 200px;
  width: 200px;
}

.skills-cell {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.modal-scrollbar::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 2px;
}

.modal-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.modal-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Custom scrollbar for forms and inputs */
.form-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.form-scrollbar::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.form-scrollbar::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 2px;
}

.form-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.form-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Utility classes for scrollbar control */
.scrollbar-none {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-none::-webkit-scrollbar {
  display: none;
}

.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 2px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Dark mode scrollbar support */
@media (prefers-color-scheme: dark) {
  * {
    scrollbar-color: #475569 #1e293b;
  }
  
  ::-webkit-scrollbar-track {
    background: #1e293b;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #475569;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: #64748b;
  }
  
  .table-scrollbar {
    scrollbar-color: #475569 #1e293b;
  }
  
  .sidebar-scrollbar {
    scrollbar-color: #475569 #1e293b;
  }
  
  .modal-scrollbar {
    scrollbar-color: #475569 #1e293b;
  }
  
  .form-scrollbar {
    scrollbar-color: #475569 #1e293b;
  }
}

/* Custom animations for better UX */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out;
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.5rem;

  /* TalentTrack Pro Corporate Blue Theme */
  --background: #ffffff;
  --foreground: #1e293b;
  --card: #ffffff;
  --card-foreground: #1e293b;
  --popover: #ffffff;
  --popover-foreground: #1e293b;

  /* Primary Blue Palette */
  --primary: #1e40af;
  --primary-foreground: #ffffff;
  --primary-hover: #1d4ed8;
  --primary-light: #3b82f6;
  --primary-dark: #1e3a8a;

  /* Secondary Colors */
  --secondary: #eff6ff;
  --secondary-foreground: #1e40af;
  --secondary-hover: #dbeafe;

  /* Muted Colors */
  --muted: #f8fafc;
  --muted-foreground: #64748b;

  /* Accent Colors */
  --accent: #eff6ff;
  --accent-foreground: #1e40af;

  /* Status Colors */
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --success: #10b981;
  --success-foreground: #ffffff;
  --warning: #f59e0b;
  --warning-foreground: #ffffff;
  --info: #06b6d4;
  --info-foreground: #ffffff;

  /* Border & Input */
  --border: #e2e8f0;
  --input: #e2e8f0;
  --input-focus: #1e40af;
  --ring: #3b82f6;

  /* Chart Colors */
  --chart-1: #1e40af;
  --chart-2: #10b981;
  --chart-3: #f59e0b;
  --chart-4: #ef4444;
  --chart-5: #06b6d4;

--sidebar: #0b1736; /* Dark navy background */
  --sidebar-foreground: #ffffff; /* Default text/icons */

  /* Active (selected) item */
  --sidebar-primary: #ffffff; /* White background for selected items */
  --sidebar-primary-foreground: #1e40af; /* Blue text for selected items */

  /* Hover state */
  --sidebar-accent: rgba(30,64,175,0.2); /* Light blue hover background */
  --sidebar-accent-foreground: #1e40af; /* Blue icons/text on hover */

  --sidebar-border: #1e3a8a; /* Divider lines */
  --sidebar-ring: #1e40af;   /* Focus ring in blue */
  --sidebar-hover: #1e3a8a;  /* Slightly darker blue hover */
  --sidebar-text-secondary: #93c5fd; /* Section headings */
  
  /* Icon colors for different categories */
  --sidebar-icon-dashboard: #3b82f6;    /* Blue for dashboard */
  --sidebar-icon-candidates: #10b981;   /* Green for candidates */
  --sidebar-icon-recruitment: #f59e0b;  /* Orange for recruitment */
  --sidebar-icon-general: #8b5cf6;      /* Purple for general */
  --sidebar-icon-user: #06b6d4;         /* Cyan for user-related */
  --sidebar-icon-system: #ef4444;       /* Red for system/logout */
}

@layer base {
  * {
    border-color: var(--border);
    outline-color: rgb(148 163 184 / 0.5);  
  }

  html,
  body {
    height: 100%;
  }

  body {
    background-color: var(--background);
    overflow-x: hidden;
    overflow-y: auto;
    color: var(--foreground);
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
      sans-serif;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    font-variation-settings: normal;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: var(--primary-dark);
    font-weight: 600;
  }
}

/* Custom utility classes */
@layer utilities {
  .shadow-top {
    box-shadow: 0 -1px 3px 0 rgb(0 0 0 / 0.1), 0 -1px 2px -1px rgb(0 0 0 / 0.1);
  }
  
  /* Ensure proper scrolling for forms */
  .form-container {
    overflow-y: auto;
    max-height: 110vh;
    padding-bottom: 1rem;
  }
  
  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }
}

/* Sidebar icon colors */
@layer components {
  /* Dashboard icon */
  .sidebar-icon-dashboard {
    color: var(--sidebar-icon-dashboard);
  }
  
  /* Candidate-related icons */
  .sidebar-icon-candidates {
    color: var(--sidebar-icon-candidates);
  }
  
  /* Recruitment-related icons */
  .sidebar-icon-recruitment {
    color: var(--sidebar-icon-recruitment);
  }
  
  /* General icons */
  .sidebar-icon-general {
    color: var(--sidebar-icon-general);
  }
  
  /* User-related icons */
  .sidebar-icon-user {
    color: var(--sidebar-icon-user);
  }
  
  /* System/logout icons */
  .sidebar-icon-system {
    color: var(--sidebar-icon-system);
  }
  
  /* Hover effects for icons */
  .sidebar-menu-button:hover svg {
    transform: scale(1.1);
    transition: transform 0.2s ease;
  }
  
  /* Active state icon colors */
  .sidebar-menu-button[data-active="true"] svg {
    color: var(--sidebar-primary-foreground);
  }
}


