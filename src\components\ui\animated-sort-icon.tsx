import { motion } from "framer-motion";
import { ChevronUp, ChevronDown, ChevronsUpDown } from "lucide-react";

interface AnimatedSortIconProps {
  /**
   * The current sort direction
   */
  direction: "ascending" | "descending" | null;
  
  /**
   * Whether this column is the active sort column
   */
  active?: boolean;
  
  /**
   * Size of the icon
   */
  size?: number;
  
  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * A component that displays an animated sort icon
 */
export function AnimatedSortIcon({
  direction,
  active = false,
  size = 16,
  className = "",
}: AnimatedSortIconProps) {
  // Default unsorted icon
  if (!active || !direction) {
    return (
      <motion.div
        whileHover={{ scale: 1.2 }}
        transition={{ duration: 0.2 }}
        className={`text-gray-400 ${className}`}
      >
        <ChevronsUpDown size={size} />
      </motion.div>
    );
  }

  // Ascending sort icon
  if (direction === "ascending") {
    return (
      <motion.div
        initial={{ scale: 1 }}
        animate={{
          scale: [1, 1.3, 1],
          rotate: [0, 0, 0],
        }}
        transition={{
          duration: 0.4,
          times: [0, 0.5, 1],
        }}
        className={`text-blue-500 ${className}`}
      >
        <ChevronUp size={size} />
      </motion.div>
    );
  }

  // Descending sort icon
  return (
    <motion.div
      initial={{ scale: 1 }}
      animate={{
        scale: [1, 1.3, 1],
        rotate: [0, 0, 0],
      }}
      transition={{
        duration: 0.4,
        times: [0, 0.5, 1],
      }}
      className={`text-blue-500 ${className}`}
    >
      <ChevronDown size={size} />
    </motion.div>
  );
}
