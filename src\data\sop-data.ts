export interface SOPSubsection {
    id: string;
    title: string;
    description: string;
    roles: string[];
    points: string[];
}

export interface SOPSection {
    id: string;
    number: number;
    title: string;
    color: string;
    subsections: SOPSubsection[];
}

export const sopData: SOPSection[] = [
    {
        id: "section-1",
        number: 1,
        title: "Receiving & Understanding the Requirement",
        color: "blue",
        subsections: [
            {
                id: "1.1",
                title: "Requirement Intake",
                description: "Initial client request processing and job posting",
                roles: ["Manager", "Respective POCs"],
                points: [
                    "Receive request from client (email/call/portal)",
                    "Posting the job description (JD), key skills, role level, location, CTC/rate, and joining timeline in the ATS"
                ]
            },
            {
                id: "1.2",
                title: "Discuss with Account Manager/Team Lead (Internal)",
                description: "Internal team discussion and documentation",
                roles: ["Recruiters"],
                points: [
                    "The team can clarify any doubts and document all details in the ATS",
                    "Get insights into client culture, team structure, and critical success factors"
                ]
            }
        ]
    },
    {
        id: "section-2",
        number: 2,
        title: "Advertising",
        color: "green",
        subsections: [
            {
                id: "2.1",
                title: "Job posting in job boards",
                description: "Post on Naukri, Monster, and other free portals",
                roles: ["Individual Recruiters"],
                points: [
                    "Post on Naukri, Monster, and other free portals"
                ]
            },
            {
                id: "2.2",
                title: "Job posting in professional communities",
                description: "Post on LinkedIn, Indeed, and other free open communities",
                roles: ["Individual Recruiters"],
                points: [
                    "Post on LinkedIn, Indeed, and other free open communities"
                ]
            },
            {
                id: "2.3",
                title: "Prepare & post flyer/poster",
                description: "Create and post flyers for critical requirements",
                roles: ["Individual Recruiters"],
                points: [
                    "Prepare & post a flyer/poster for a critical requirement"
                ]
            }
        ]
    },
    {
        id: "section-3",
        number: 3,
        title: "Sourcing Candidates",
        color: "purple",
        subsections: [
            {
                id: "3.1",
                title: "Sourcing Channels",
                description: "Various channels for candidate sourcing",
                roles: ["Recruiters"],
                points: [
                    "Internal database/ATS",
                    "Job portals (e.g., Naukri, Monster, etc.)",
                    "Employee referrals",
                    "Professional Networks like: LinkedIn, Indeed, GitHub, Stackoverflow, Trailblazers etc.",
                    "Headhunting"
                ]
            },
            {
                id: "3.2",
                title: "Sourcing Methods",
                description: "Advanced search techniques and AI tools",
                roles: ["Recruiters"],
                points: [
                    "Use relevant keywords",
                    "Use Boolean logic (AND, OR & NOT)",
                    "Use Xray search",
                    "Leverage AI Tools like: Chat GPT, Deepseek, Gemini, Perplexity etc to prompt for analyzing the sourcing techniques"
                ]
            }
        ]
    },
    {
        id: "section-4",
        number: 4,
        title: "Screening Process",
        color: "orange",
        subsections: [
            {
                id: "4.1",
                title: "Telephonic/Virtual Screening",
                description: "Initial candidate screening and assessment",
                roles: ["Recruiters"],
                points: [
                    "Introducing self and the firm to the candidate with communication & high confidence",
                    "Discuss the role, client, and the responsibilities to the candidate in detail",
                    "Leverage AI tools to evaluate technical and non-technical skills",
                    "Assess communication, availability, and intent",
                    "Confirm notice period, current and expected CTC/rate, and willingness to relocate/travel (if required), amongst other details"
                ]
            },
            {
                id: "4.2",
                title: "Candidate Record",
                description: "Documentation and ATS updates",
                roles: ["Recruiters"],
                points: [
                    "Immediately register the candidate in the ATS - with a detailed remark mentioned",
                    "Assign it to peer review (if needed)",
                    "It is mandatory that every individual recruiter has to update the candidate status every day on or before 10:45 AM"
                ]
            }
        ]
    },
    {
        id: "section-5",
        number: 5,
        title: "Submission to Client",
        color: "teal",
        subsections: [
            {
                id: "5.1",
                title: "Candidate Submission",
                description: "Client submission process",
                roles: ["Recruiters"],
                points: [
                    "Get an email confirmation from the candidate outlining his/her acceptance to the JD and the company",
                    "Send shortlisted profiles to the client with the candidate summary and detailed tracker"
                ]
            }
        ]
    },
    {
        id: "section-6",
        number: 6,
        title: "Interview Coordination",
        color: "indigo",
        subsections: [
            {
                id: "6.1",
                title: "Interview Scheduling",
                description: "Coordinating interviews between candidates and clients",
                roles: ["Recruiters"],
                points: [
                    "Coordinate between candidate and client for interview slots",
                    "Send/coordinate interview invitations/emails to both parties",
                    "Share candidate preparation tips and reshare the JD with the candidate"
                ]
            },
            {
                id: "6.2",
                title: "Follow-ups",
                description: "Interview follow-up and feedback management",
                roles: ["Recruiters"],
                points: [
                    "Remind candidates of scheduled interviews (email, Whatsapp, messages)",
                    "Ensure timely feedback from both client and candidate post-interview",
                    "In case the interview feedback is taking more than 48 hrs, inform your respective manager"
                ]
            }
        ]
    },
    {
        id: "section-7",
        number: 7,
        title: "Offer & Onboarding",
        color: "pink",
        subsections: [
            {
                id: "7.1",
                title: "Offer Management",
                description: "Managing offer process and negotiations",
                roles: ["Recruiters"],
                points: [
                    "Obtain client approval for selected candidate",
                    "Share offer details and guide candidate through acceptance process",
                    "Negotiate as necessary while keeping stakeholders informed"
                ]
            },
            {
                id: "7.2",
                title: "Documentation",
                description: "Document collection and verification",
                roles: ["Respective POCs", "HR"],
                points: [
                    "Respective POCs to collect and verify necessary documents (ID, education, experience proofs, etc.)",
                    "For contract staff, ensure background checks as per client requirements – HR"
                ]
            }
        ]
    },
    {
        id: "section-8",
        number: 8,
        title: "Post Placement Follow-Up",
        color: "red",
        subsections: [
            {
                id: "8.1",
                title: "Follow-up Schedule",
                description: "Post-placement monitoring and support for one-time placements",
                roles: ["Recruiters"],
                points: [
                    "Check in with client and candidate within the first week and at agreed intervals (30, 60, 90 days)",
                    "Resolve any issues proactively",
                    "Monitor candidate performance and client satisfaction",
                    "Document any challenges or success stories for future reference",
                    "Maintain relationship for potential future opportunities"
                ]
            }
        ]
    }
];

export const getColorClasses = (color: string) => {
    const colorMap = {
        blue: {
            bg: "bg-blue-500",
            bgLight: "from-blue-50 to-blue-100",
            border: "border-blue-200",
            text: "text-blue-900",
            textLight: "text-blue-700",
            textDark: "text-blue-800",
            dot: "bg-blue-500",
            tag: "bg-blue-500",
            tagLight: "bg-blue-400"
        },
        green: {
            bg: "bg-green-500",
            bgLight: "from-green-50 to-green-100",
            border: "border-green-200",
            text: "text-green-900",
            textLight: "text-green-700",
            textDark: "text-green-800",
            dot: "bg-green-500",
            tag: "bg-green-500",
            tagLight: "bg-green-400"
        },
        purple: {
            bg: "bg-purple-500",
            bgLight: "from-purple-50 to-purple-100",
            border: "border-purple-200",
            text: "text-purple-900",
            textLight: "text-purple-700",
            textDark: "text-purple-800",
            dot: "bg-purple-500",
            tag: "bg-purple-500",
            tagLight: "bg-purple-400"
        },
        orange: {
            bg: "bg-orange-500",
            bgLight: "from-orange-50 to-orange-100",
            border: "border-orange-200",
            text: "text-orange-900",
            textLight: "text-orange-700",
            textDark: "text-orange-800",
            dot: "bg-orange-500",
            tag: "bg-orange-500",
            tagLight: "bg-orange-400"
        },
        teal: {
            bg: "bg-teal-500",
            bgLight: "from-teal-50 to-teal-100",
            border: "border-teal-200",
            text: "text-teal-900",
            textLight: "text-teal-700",
            textDark: "text-teal-800",
            dot: "bg-teal-500",
            tag: "bg-teal-500",
            tagLight: "bg-teal-400"
        },
        indigo: {
            bg: "bg-indigo-500",
            bgLight: "from-indigo-50 to-indigo-100",
            border: "border-indigo-200",
            text: "text-indigo-900",
            textLight: "text-indigo-700",
            textDark: "text-indigo-800",
            dot: "bg-indigo-500",
            tag: "bg-indigo-500",
            tagLight: "bg-indigo-400"
        },
        pink: {
            bg: "bg-pink-500",
            bgLight: "from-pink-50 to-pink-100",
            border: "border-pink-200",
            text: "text-pink-900",
            textLight: "text-pink-700",
            textDark: "text-pink-800",
            dot: "bg-pink-500",
            tag: "bg-pink-500",
            tagLight: "bg-pink-400"
        },
        red: {
            bg: "bg-red-500",
            bgLight: "from-red-50 to-red-100",
            border: "border-red-200",
            text: "text-red-900",
            textLight: "text-red-700",
            textDark: "text-red-800",
            dot: "bg-red-500",
            tag: "bg-red-500",
            tagLight: "bg-red-400"
        }
    };

    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
};
