import { useCandidates, useCandidatesCacheStatus } from '@/contexts/candidates-context';

/**
 * Hook to get candidates from API only
 * This hook provides a simple interface for components that need candidate data
 */
export function useCachedCandidates() {
  const {
    candidates: apiCandidates,
    loading,
    error,
    refreshCandidates,
    fetchCandidates,
    lastFetched,
  } = useCandidates();

  const { hasCache, isCacheValid } = useCandidatesCacheStatus();

  // Use API data only - no demo data fallback
  const candidates = apiCandidates;

  // Function to ensure fresh data (will use cache if valid, otherwise fetch)
  const ensureFreshData = async () => {
    if (!isCacheValid) {
      await fetchCandidates();
    }
  };

  // Function to force refresh data
  const forceRefresh = async () => {
    await refreshCandidates();
  };

  return {
    candidates,
    loading,
    error,
    hasCache,
    isCacheValid,
    lastFetched,
    ensureFreshData,
    forceRefresh,
    isUsingFallbackData: false, // No fallback data used
  };
}
