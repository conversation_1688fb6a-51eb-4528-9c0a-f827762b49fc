import React, { createContext, useContext, useState, useEffect, useCallback } from "react";
import { AuthService, LoginCredentials } from "@/services/auth";
import { ApiService } from "@/services/api";

// Define user roles
export type UserRole = "manager" | "recruiter" | null;

// Define user context type
interface UserContextType {
  isAuthenticated: boolean;
  userRole: UserRole;
  userName: string;
  name: string;
  userEmail: string;
  userId: string;
  peerStatus: boolean;
  userAvatar: string | undefined;
  isLoadingAvatar: boolean;
  login: (credentials: LoginCredentials, role: UserRole) => Promise<void>;
  logout: () => void;
  loadUserAvatar: () => Promise<void>;
  updateUserAvatar: (avatarData: string) => void;
  onLoginSuccess?: () => void; // Callback for post-login actions
  setLoginSuccessCallback: (callback: () => void) => void; // Callback setter
}

// Create context with default values
const UserContext = createContext<UserContextType>({
  isAuthenticated: false,
  userRole: null,
  userName: "",
  name: "",
  userEmail: "",
  userId: "",
  peerStatus: false,
  userAvatar: undefined,
  isLoadingAvatar: false,
  login: async () => { },
  logout: () => { },
  loadUserAvatar: async () => { },
  updateUserAvatar: () => { },
  onLoginSuccess: () => { },
  setLoginSuccessCallback: () => { },
});

// Hook to use the user context
export const useUser = () => useContext(UserContext);

// Provider component
export const UserProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(
    localStorage.getItem("isAuthenticated") === "true"
  );
  const [userRole, setUserRole] = useState<UserRole>(
    (localStorage.getItem("userRole") as UserRole) || null
  );
  const [userName, setUserName] = useState<string>(
    localStorage.getItem("userName") || ""
  );
  const [userEmail, setUserEmail] = useState<string>(
    localStorage.getItem("userEmail") || ""
  );
  const [userId, setUserId] = useState<string>(
    localStorage.getItem("userId") || ""
  );
  const [name, setName] = useState<string>(localStorage.getItem("name") || "");
  const [peerStatus, setPeerStatus] = useState<boolean>(
    localStorage.getItem("peerStatus") === "true"
  );
  const [userAvatar, setUserAvatar] = useState<string | undefined>(() => {
    const userId = localStorage.getItem("userId");
    if (userId) {
      const cachedAvatar = localStorage.getItem(`user_avatar_${userId}`);
      if (cachedAvatar && cachedAvatar !== "null" && cachedAvatar !== "undefined") {
        return cachedAvatar;
      }
    }
    return undefined;
  });
  const [isLoadingAvatar, setIsLoadingAvatar] = useState<boolean>(false);

  // Callback for post-login actions (like data pre-fetching)
  const [onLoginSuccess, setOnLoginSuccess] = useState<(() => void) | undefined>(undefined);

  // Load user avatar function
  const loadUserAvatar = async (): Promise<void> => {
    const currentUserId = localStorage.getItem("userId");
    if (!currentUserId) return;

    // Check if we already have a cached avatar
    const cachedAvatar = localStorage.getItem(`user_avatar_${currentUserId}`);
    if (cachedAvatar && cachedAvatar !== "null" && cachedAvatar !== "undefined") {
      setUserAvatar(cachedAvatar);
      return;
    }

    setIsLoadingAvatar(true);
    try {
      const avatarData = await ApiService.getUserImage(currentUserId);
      if (avatarData) {
        setUserAvatar(avatarData);
        localStorage.setItem(`user_avatar_${currentUserId}`, avatarData);
      }
    } catch (error) {
      // Failed to load user avatar
    } finally {
      setIsLoadingAvatar(false);
    }
  };

  // Update user avatar function
  const updateUserAvatar = (avatarData: string): void => {
    setUserAvatar(avatarData);
    const currentUserId = localStorage.getItem("userId");
    if (currentUserId) {
      if (avatarData) {
        localStorage.setItem(`user_avatar_${currentUserId}`, avatarData);
      } else {
        localStorage.removeItem(`user_avatar_${currentUserId}`);
      }
    }
  };

  // Set login success callback
  const setLoginSuccessCallback = useCallback((callback: () => void) => {
    setOnLoginSuccess(() => callback);
  }, []);

  // Load avatar when user is authenticated and userId changes
  useEffect(() => {
    if (isAuthenticated && userId) {
      loadUserAvatar();
    }
  }, [isAuthenticated, userId]);

  // Login function
  const login = async (
    credentials: LoginCredentials,
    role: UserRole
  ): Promise<void> => {
    if (!role) {
      throw new Error("Role is required for login");
    }

    try {
      // Call the authentication API
      const response = await AuthService.login(credentials, role);

      if (response.status !== "success") {
        throw new Error(response.message || "Login failed");
      }

      // Set user data from API response
      if (response.email && response.user_id) {
        setIsAuthenticated(true);
        setUserRole(role);
        setUserEmail(response.email);
        setUserName(credentials.username);
        setUserId(response.user_id.toString());
        setName(response.name || "");
        setPeerStatus(response.peer_status || false);

        // Store in localStorage
        localStorage.setItem("isAuthenticated", "true");
        localStorage.setItem("userRole", role);
        localStorage.setItem("userName", credentials.username);
        localStorage.setItem("userEmail", response.email);
        localStorage.setItem("userId", response.user_id.toString());
        localStorage.setItem("name", response.name || "");
        localStorage.setItem(
          "peerStatus",
          (response.peer_status || false).toString()
        );

        if (response.token) {
          localStorage.setItem("authToken", response.token);
        }

        // Trigger post-login actions (like data pre-fetching)
        if (onLoginSuccess) {
          onLoginSuccess();
        }
      } else {
        throw new Error("Invalid response from server");
      }
    } catch (error) {
      // Login failed
      throw error;
    }
  };

  // Helper function to clear all possible data sources
  const clearAllDataSources = () => {
    const currentUserId = localStorage.getItem("userId");

    // Clear all localStorage items systematically
    const keysToRemove = [
      "isAuthenticated", "userRole", "userName", "userEmail", "userId",
      "authToken", "name", "peerStatus", "recruiterTargets",
      "rememberedUsername", "user_id", "raise_issue_images", "username", "email"
    ];

    keysToRemove.forEach(key => localStorage.removeItem(key));

    // Clear any recruiter-specific targets
    if (currentUserId) {
      localStorage.removeItem(`recruiterTargets_${currentUserId}`);
      localStorage.removeItem(`user_avatar_${currentUserId}`);
    }

    // Clear any other potential recruiter targets and user-specific data (clean up all)
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (
        key.startsWith('recruiterTargets_') ||
        key.startsWith('user_avatar_') ||
        key.includes('recruiter') ||
        key.includes('target') ||
        key.includes('user') ||
        key.includes('profile') ||
        key.includes('candidate') ||
        key.includes('job') ||
        key.includes('client')
      )) {
        localStorage.removeItem(key);
      }
    }

    // Clear cookies
    document.cookie = "sidebar_state=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
    document.cookie = "sidebar_state=; path=/; max-age=0";

    // Clear any other potential cookies
    const cookies = document.cookie.split(";");
    cookies.forEach(cookie => {
      const eqPos = cookie.indexOf("=");
      const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
      document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;
    });

    // Clear any potential sessionStorage
    try {
      sessionStorage.clear();
    } catch (e) {
      // SessionStorage might not be available
    }

    // Clear any potential IndexedDB (if it exists)
    try {
      if ('indexedDB' in window) {
        indexedDB.deleteDatabase('app-db');
      }
    } catch (e) {
      // IndexedDB might not be available
    }

    // Clear any potential WebSQL (if it exists)
    try {
      if ('openDatabase' in window) {
        // WebSQL is deprecated but might still exist
        const db = (window as any).openDatabase('app-db', '1.0', 'App Database', 2 * 1024 * 1024);
        if (db) {
          db.close();
        }
      }
    } catch (e) {
      // WebSQL might not be available
    }

    // Clear any potential cache storage (if it exists)
    try {
      if ('caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => {
            caches.delete(name);
          });
        });
      }
    } catch (e) {
      // Cache API might not be available
    }

    // Clear any potential service worker registrations
    try {
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.getRegistrations().then(registrations => {
          registrations.forEach(registration => {
            registration.unregister();
          });
        });
      }
    } catch (e) {
      // Service Worker API might not be available
    }

    // Clear any potential application cache (if it exists)
    try {
      if ('applicationCache' in window) {
        const appCache = (window as any).applicationCache;
        if (appCache) {
          appCache.clear();
        }
      }
    } catch (e) {
      // Application Cache might not be available
    }

    // Clear any potential file system access (if it exists)
    try {
      if ('showDirectoryPicker' in window) {
        // Clear any file handles that might be stored
        // This is a newer API, so we'll just try to clear any potential storage
      }
    } catch (e) {
      // File System Access API might not be available
    }

    // Clear any potential notification permissions
    try {
      if ('Notification' in window && 'permission' in Notification) {
        // We can't clear permissions, but we can revoke them
        if (Notification.permission === 'granted') {
          // Note: We can't programmatically revoke notification permissions
          // This is a browser security feature
        }
      }
    } catch (e) {
      // Notification API might not be available
    }

    // Clear any potential push manager subscriptions
    try {
      if ('serviceWorker' in navigator && 'PushManager' in window) {
        navigator.serviceWorker.ready.then(registration => {
          registration.pushManager.getSubscription().then(subscription => {
            if (subscription) {
              subscription.unsubscribe();
            }
          });
        });
      }
    } catch (e) {
      // Push Manager API might not be available
    }

    // Clear any potential background sync registrations
    try {
      if ('serviceWorker' in navigator && 'sync' in window) {
        navigator.serviceWorker.ready.then(registration => {
          // Clear any background sync tags
          // Note: We can't list all sync tags, but we can try to clear common ones
          const commonTags = ['background-sync', 'data-sync', 'user-sync'];
          commonTags.forEach(tag => {
            try {
              (registration as any).sync.unregister(tag);
            } catch (e) {
              // Tag might not exist
            }
          });
        });
      }
    } catch (e) {
      // Background Sync API might not be available
    }

    // Clear any potential periodic background sync registrations
    try {
      if ('serviceWorker' in navigator && 'periodicSync' in window) {
        navigator.serviceWorker.ready.then(registration => {
          // Clear any periodic sync tags
          const commonTags = ['periodic-sync', 'data-sync', 'user-sync'];
          commonTags.forEach(tag => {
            try {
              (registration as any).periodicSync.unregister(tag);
            } catch (e) {
              // Tag might not exist
            }
          });
        });
      }
    } catch (e) {
      // Periodic Background Sync API might not be available
    }

    // Clear any potential payment method data
    try {
      if ('PaymentRequest' in window) {
        // Clear any stored payment methods
        // Note: We can't programmatically clear payment methods due to security
        // This is just a placeholder for future implementation
      }
    } catch (e) {
      // Payment Request API might not be available
    }

    // Clear any potential credential management data
    try {
      if ('credentials' in navigator) {
        // Clear any stored credentials
        navigator.credentials.get({}).then(credential => {
          if (credential) {
            // We can't programmatically clear credentials due to security
            // This is just a placeholder for future implementation
          }
        });
      }
    } catch (e) {
      // Credential Management API might not be available
    }

    // Clear any potential device orientation data
    try {
      if ('DeviceOrientationEvent' in window) {
        // Clear any device orientation event listeners
        // Note: We can't programmatically clear device orientation data due to security
        // This is just a placeholder for future implementation
      }
    } catch (e) {
      // Device Orientation API might not be available
    }

    // Clear any potential device motion data
    try {
      if ('DeviceMotionEvent' in window) {
        // Clear any device motion event listeners
        // Note: We can't programmatically clear device motion data due to security
        // This is just a placeholder for future implementation
      }
    } catch (e) {
      // Device Motion API might not be available
    }

    // Clear any potential geolocation data
    try {
      if ('geolocation' in navigator) {
        // Clear any geolocation data
        // Note: We can't programmatically clear geolocation data due to security
        // This is just a placeholder for future implementation
      }
    } catch (e) {
      // Geolocation API might not be available
    }

    // Clear any potential media device data
    try {
      if ('mediaDevices' in navigator) {
        // Clear any media device data
        // Note: We can't programmatically clear media device data due to security
        // This is just a placeholder for future implementation
      }
    } catch (e) {
      // Media Devices API might not be available
    }

    // Clear any potential battery data
    try {
      if ('getBattery' in navigator) {
        // Clear any battery data
        // Note: We can't programmatically clear battery data due to security
        // This is just a placeholder for future implementation
      }
    } catch (e) {
      // Battery API might not be available
    }

    // Clear any potential network information data
    try {
      if ('connection' in navigator) {
        // Clear any network information data
        // Note: We can't programmatically clear network information data due to security
        // This is just a placeholder for future implementation
      }
    } catch (e) {
      // Network Information API might not be available
    }

    // Clear any potential hardware concurrency data
    try {
      if ('hardwareConcurrency' in navigator) {
        // Clear any hardware concurrency data
        // Note: We can't programmatically clear hardware concurrency data due to security
        // This is just a placeholder for future implementation
      }
    } catch (e) {
      // Hardware Concurrency API might not be available
    }

    // Clear any potential device memory data
    try {
      if ('deviceMemory' in navigator) {
        // Clear any device memory data
        // Note: We can't programmatically clear device memory data due to security
        // This is just a placeholder for future implementation
      }
    } catch (e) {
      // Device Memory API might not be available
    }

    // Clear any potential user agent data
    try {
      if ('userAgentData' in navigator) {
        // Clear any user agent data
        // Note: We can't programmatically clear user agent data due to security
        // This is just a placeholder for future implementation
      }
    } catch (e) {
      // User Agent Data API might not be available
    }

    // Clear any potential platform data
    try {
      if ('platform' in navigator) {
        // Clear any platform data
        // Note: We can't programmatically clear platform data due to security
        // This is just a placeholder for future implementation
      }
    } catch (e) {
      // Platform API might not be available
    }

    // Clear any potential language data
    try {
      if ('language' in navigator) {
        // Clear any language data
        // Note: We can't programmatically clear language data due to security
        // This is just a placeholder for future implementation
      }
    } catch (e) {
      // Language API might not be available
    }
  };

  // Logout function
  const logout = () => {
    // Clear all data sources first
    clearAllDataSources();

    // Clear all state
    setIsAuthenticated(false);
    setUserRole(null);
    setUserName("");
    setUserEmail("");
    setUserId("");
    setName("");
    setPeerStatus(false);
    setUserAvatar(undefined);
    setIsLoadingAvatar(false);

    // Note: Redux stores are cleared in the logout component
    // Candidates context will clear automatically when userEmail becomes empty
  };



  return (
    <UserContext.Provider
      value={{
        isAuthenticated,
        userRole,
        userName,
        name,
        userEmail,
        userId,
        peerStatus,
        userAvatar,
        isLoadingAvatar,
        login,
        logout,
        loadUserAvatar,
        updateUserAvatar,
        onLoginSuccess,
        setLoginSuccessCallback,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};
