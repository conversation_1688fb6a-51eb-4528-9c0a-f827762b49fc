import { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useAppSelector } from "@/store/hooks";
import { selectJobs, selectJobsLoading } from "@/store/selectors/jobsSelectors";
import { selectCandidates } from "@/store/selectors/candidatesSelectors";
import {
    ArrowLeft,
    Search,
    Users,
    CheckCircle,
    XCircle,
    Clock,
    Target,
    Building2,
    Calendar,
    MapPin,
    Briefcase,
    UserCheck
} from "lucide-react";
import { AnimatedTableWrapper } from "@/components/ui/animated-table-wrapper";
import { TableSkeleton } from "@/components/ui/table-skeleton";
import { AnimatedPagination } from "@/components/ui/animated-pagination";
import { useTableAnimation } from "@/hooks/use-table-animation";

export function JobDetailPage() {
    const navigate = useNavigate();
    const { jobId } = useParams<{ jobId: string }>();
    const jobs = useAppSelector(selectJobs);
    const candidates = useAppSelector(selectCandidates);
    const jobsLoading = useAppSelector(selectJobsLoading);

    // Local state
    const [searchTerm, setSearchTerm] = useState("");
    const [statusFilter, setStatusFilter] = useState<string>("all");
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(10);

    // Table animation
    const { animatePagination } = useTableAnimation();

    // Find the specific job
    const job = jobs.find(j => j.id.toString() === jobId);

    // Filter candidates for this specific job
    const jobCandidates = candidates.filter(candidate =>
        candidate.jobId === jobId || candidate.client === job?.client
    );

    // Calculate candidate statistics
    const candidateStats = {
        totalCandidates: jobCandidates.length,
        selected: jobCandidates.filter(c => c.status === 'Selected' || c.status === "Onboarded").length,
        rejected: jobCandidates.filter(c =>
            c.status === 'Rejected' || c.status === 'PR-Rejected' || c.status === "L3 - REJECTED"
            || c.status === 'DROP' || c.status === 'Client – Duplicate Profile' || c.status === 'Client Screening – Rejected'
            || c.status === 'Candidate – Dropped' || c.status === 'Interview No-show' || c.status === 'DUPLICATE'
            || c.status === 'CANDIDATE NO - SHOW' || c.status === "L1 - REJECTED" || c.status === "L2 - REJECTED"
        ).length,
        inProgress: 0 // Will be calculated below
    };

    // Calculate inProgress using the formula: total - selected - rejected
    candidateStats.inProgress = candidateStats.totalCandidates - candidateStats.selected - candidateStats.rejected;

    // Filter candidates based on search and status
    const filteredCandidates = jobCandidates.filter(candidate => {
        const matchesSearch =
            `${candidate.firstName} ${candidate.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
            candidate.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            candidate.phone?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            candidate.recruiter?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            candidate.status?.toLowerCase().includes(searchTerm.toLowerCase());

        let matchesStatus = true;
        if (statusFilter !== "all") {
            if (statusFilter === "selected") {
                matchesStatus = candidate.status === 'Selected' || candidate.status === "Onboarded";
            } else if (statusFilter === "rejected") {
                matchesStatus = candidate.status === 'Rejected' || candidate.status === 'PR-Rejected' || candidate.status === "L3 - REJECTED"
                    || candidate.status === 'DROP' || candidate.status === 'Client – Duplicate Profile' || candidate.status === 'Client Screening – Rejected'
                    || candidate.status === 'Candidate – Dropped' || candidate.status === 'Interview No-show' || candidate.status === 'DUPLICATE'
                    || candidate.status === 'CANDIDATE NO - SHOW' || candidate.status === "L1 - REJECTED" || candidate.status === "L2 - REJECTED";
            } else if (statusFilter === "inProgress") {
                // Use the same logic as the statistics calculation
                const isSelected = candidate.status === 'Selected' || candidate.status === "Onboarded";
                const isRejected = candidate.status === 'Rejected' || candidate.status === 'PR-Rejected' || candidate.status === "L3 - REJECTED"
                    || candidate.status === 'DROP' || candidate.status === 'Client – Duplicate Profile' || candidate.status === 'Client Screening – Rejected'
                    || candidate.status === 'Candidate – Dropped' || candidate.status === 'Interview No-show' || candidate.status === 'DUPLICATE'
                    || candidate.status === 'CANDIDATE NO - SHOW' || candidate.status === "L1 - REJECTED" || candidate.status === "L2 - REJECTED";
                matchesStatus = !isSelected && !isRejected;
            }
        }

        return matchesSearch && matchesStatus;
    });

    // Pagination
    const totalPages = Math.ceil(filteredCandidates.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const paginatedCandidates = filteredCandidates.slice(startIndex, startIndex + itemsPerPage);

    // Handle page change
    const handlePageChange = async (page: number) => {
        await animatePagination();
        setCurrentPage(page);
    };

    // Handle card clicks for filtering
    const handleCardClick = (status: string) => {
        if (statusFilter === status) {
            setStatusFilter("all"); // Toggle off if already selected
        } else {
            setStatusFilter(status);
        }
        setCurrentPage(1); // Reset to first page
    };

    // Get status styling for candidates
    const getCandidateStatusStyling = (status: string) => {
        switch (status) {
            case 'Selected':
            case 'PR-Approved':
                return {
                    badge: 'bg-green-100 text-green-800',
                    icon: CheckCircle,
                    iconColor: 'text-green-600'
                };
            case 'Rejected':
                return {
                    badge: 'bg-red-100 text-red-800',
                    icon: XCircle,
                    iconColor: 'text-red-600'
                };
            default:
                return {
                    badge: 'bg-yellow-100 text-yellow-800',
                    icon: Clock,
                    iconColor: 'text-yellow-600'
                };
        }
    };

    // Stat Card Component
    const StatCard = ({ title, value, icon: Icon, color, trend, onClick, isSelected }: {
        title: string;
        value: number;
        icon: any;
        color: string;
        trend?: string;
        onClick?: () => void;
        isSelected?: boolean;
    }) => (
        <div
            className={`bg-white rounded-lg shadow-md p-6 border-2 transition-all ${onClick ? 'cursor-pointer hover:shadow-lg' : ''} ${isSelected ? 'border-blue-500 shadow-lg' : 'border-gray-200'}`}
            onClick={onClick}
        >
            <div className="flex items-center justify-between">
                <div>
                    <p className="text-sm font-medium text-gray-600">{title}</p>
                    <p className="text-3xl font-bold text-gray-900">{value}</p>
                    {trend && (
                        <p className="text-sm text-green-600 mt-1">{trend}</p>
                    )}
                </div>
                <div className={`p-3 rounded-full ${color}`}>
                    <Icon className="h-6 w-6 text-white" />
                </div>
            </div>
        </div>
    );

    // If job not found, show error
    if (!job && !jobsLoading) {
        return (
            <div className="flex-1 p-6 bg-gray-50 overflow-y-auto h-full">
                <div className="max-w-7xl mx-auto">
                    <div className="text-center py-12">
                        <h2 className="text-2xl font-bold text-gray-900 mb-4">Job Not Found</h2>
                        <p className="text-gray-600 mb-6">The job you're looking for doesn't exist or has been removed.</p>
                        <button
                            onClick={() => navigate("/manager/jobs-overview")}
                            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                            Back to Jobs Overview
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="flex-1 p-6 bg-gray-50 overflow-y-auto h-full">
            <div className="max-w-7xl mx-auto">
                {/* Header */}
                <div className="mb-8 flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <button
                            onClick={() => navigate("/manager/jobs-overview")}
                            className="p-2 rounded-lg bg-white border border-gray-200 hover:bg-gray-50 transition-colors"
                        >
                            <ArrowLeft className="h-5 w-5 text-gray-600" />
                        </button>
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900">
                                {job?.role || 'Loading...'} - {job?.location || ''}
                            </h1>
                            <p className="text-gray-600 mt-2">Job Details and Candidates</p>
                        </div>
                    </div>
                    {job && (
                        <span className={`px-3 py-1 text-sm font-medium rounded-full ${job.job_status === 'Active' ? 'bg-green-100 text-green-800' :
                            job.job_status === 'Hold' || job.job_status === 'On Hold' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-red-100 text-red-800'
                            }`}>
                            {job.job_status}
                        </span>
                    )}
                </div>

                {/* Job Status Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <StatCard
                        title="Total Candidates"
                        value={candidateStats.totalCandidates}
                        icon={Users}
                        color="bg-purple-500"
                        trend="All Candidates"
                        onClick={() => handleCardClick("all")}
                        isSelected={statusFilter === "all"}
                    />
                    <StatCard
                        title="Selected"
                        value={candidateStats.selected}
                        icon={CheckCircle}
                        color="bg-green-500"
                        trend="Approved"
                        onClick={() => handleCardClick("selected")}
                        isSelected={statusFilter === "selected"}
                    />
                    <StatCard
                        title="Rejected"
                        value={candidateStats.rejected}
                        icon={XCircle}
                        color="bg-red-500"
                        trend="Not Selected"
                        onClick={() => handleCardClick("rejected")}
                        isSelected={statusFilter === "rejected"}
                    />
                    <StatCard
                        title="In Progress"
                        value={candidateStats.inProgress}
                        icon={Clock}
                        color="bg-yellow-500"
                        trend="Under Review"
                        onClick={() => handleCardClick("inProgress")}
                        isSelected={statusFilter === "inProgress"}
                    />
                </div>

                {/* Job Details */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    {/* Job Information */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Job Information</h3>
                        <div className="space-y-3">
                            <div className="flex items-center space-x-3">
                                <Target className="h-5 w-5 text-gray-400" />
                                <span className="text-sm text-gray-600">Job ID:</span>
                                <span className="text-sm font-medium text-gray-900">#{job?.id}</span>
                            </div>
                            <div className="flex items-center space-x-3">
                                <Briefcase className="h-5 w-5 text-gray-400" />
                                <span className="text-sm text-gray-600">Role:</span>
                                <span className="text-sm font-medium text-gray-900">{job?.role || 'N/A'}</span>
                            </div>
                            <div className="flex items-center space-x-3">
                                <MapPin className="h-5 w-5 text-gray-400" />
                                <span className="text-sm text-gray-600">Location:</span>
                                <span className="text-sm font-medium text-gray-900">{job?.location || 'N/A'}</span>
                            </div>
                            <div className="flex items-center space-x-3">
                                <Briefcase className="h-5 w-5 text-gray-400" />
                                <span className="text-sm text-gray-600">Job Type:</span>
                                <span className="text-sm font-medium text-gray-900">{job?.job_type || 'N/A'}</span>
                            </div>
                            <div className="flex items-center space-x-3">
                                <CheckCircle className="h-5 w-5 text-gray-400" />
                                <span className="text-sm text-gray-600">Status:</span>
                                <span className="text-sm font-medium text-gray-900">{job?.job_status || 'N/A'}</span>
                            </div>
                        </div>
                    </div>

                    {/* Recruitment Details */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Recruitment Details</h3>
                        <div className="space-y-3">
                            <div className="flex items-center space-x-3">
                                <Users className="h-5 w-5 text-gray-400" />
                                <span className="text-sm text-gray-600">Recruiter:</span>
                                <span className="text-sm font-medium text-gray-900">{job?.recruiter || 'N/A'}</span>
                            </div>
                            <div className="flex items-center space-x-3">
                                <Target className="h-5 w-5 text-gray-400" />
                                <span className="text-sm text-gray-600">Positions:</span>
                                <span className="text-sm font-medium text-gray-900">{job?.no_of_positions || 'N/A'}</span>
                            </div>
                            <div className="flex items-center space-x-3">
                                <Calendar className="h-5 w-5 text-gray-400" />
                                <span className="text-sm text-gray-600">Posted Date:</span>
                                <span className="text-sm font-medium text-gray-900">
                                    {job?.date_created ? new Date(job.date_created).toLocaleDateString() : 'N/A'}
                                </span>
                            </div>
                            <div className="flex items-center space-x-3">
                                <Building2 className="h-5 w-5 text-gray-400" />
                                <span className="text-sm text-gray-600">Client:</span>
                                <span className="text-sm font-medium text-gray-900">{job?.client || 'N/A'}</span>
                            </div>
                            <div className="flex items-center space-x-3">
                                <UserCheck className="h-5 w-5 text-gray-400" />
                                <span className="text-sm text-gray-600">Management:</span>
                                <span className="text-sm font-medium text-gray-900">{job?.management || 'N/A'}</span>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Search and Items Per Page */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                    <div className="flex flex-col md:flex-row gap-4">
                        {/* Search */}
                        <div className="flex-1">
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                                <input
                                    type="text"
                                    placeholder="Search candidates by name, email, phone, recruiter, or status..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>
                        </div>

                        {/* Items per page */}
                        <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">Show:</span>
                            <select
                                value={itemsPerPage}
                                onChange={(e) => {
                                    setItemsPerPage(Number(e.target.value));
                                    setCurrentPage(1);
                                }}
                                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            >
                                <option value={10}>10</option>
                                <option value={25}>25</option>
                                <option value={50}>50</option>
                                <option value={100}>100</option>
                            </select>
                        </div>
                    </div>
                </div>

                {/* Candidates Table */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div className="px-4 py-2 border-b border-gray-200">
                        <div className="flex items-center justify-between">
                            <div>
                                <h3 className="text-lg font-medium text-gray-900">
                                    Candidates for this Job ({filteredCandidates.length} found)
                                </h3>
                                <p className="text-sm text-gray-600 mt-1">Showing all candidates associated with this position</p>
                            </div>
                            {statusFilter !== "all" && (
                                <button
                                    onClick={() => setStatusFilter("all")}
                                    className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                                >
                                    Clear Filter
                                </button>
                            )}
                        </div>
                    </div>

                    {jobsLoading ? (
                        <TableSkeleton />
                    ) : (
                        <AnimatedTableWrapper>
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Job ID
                                            </th>
                                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Name
                                            </th>
                                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Email
                                            </th>
                                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Mobile
                                            </th>
                                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider max-w-xs">
                                                Recruiter
                                            </th>
                                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Status
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {paginatedCandidates.length > 0 ? (
                                            paginatedCandidates.map((candidate) => {
                                                const statusStyling = getCandidateStatusStyling(candidate.status || '');
                                                const StatusIcon = statusStyling.icon;

                                                return (
                                                    <tr key={candidate.id} className="hover:bg-gray-50">
                                                        <td className="px-4 py-2 whitespace-nowrap">
                                                            <div className="text-sm font-medium text-gray-900">
                                                                #{job?.id}
                                                            </div>
                                                        </td>
                                                        <td className="px-4 py-2 whitespace-nowrap">
                                                            <div className="text-sm font-medium text-gray-900">
                                                                {`${candidate.firstName || ''} ${candidate.lastName || ''}`.trim() || 'N/A'}
                                                            </div>
                                                        </td>
                                                        <td className="px-4 py-2 whitespace-nowrap">
                                                            <div className="text-sm text-gray-900">
                                                                {candidate.email || 'N/A'}
                                                            </div>
                                                        </td>
                                                        <td className="px-4 py-2 whitespace-nowrap">
                                                            <div className="text-sm text-gray-900">
                                                                {candidate.phone || 'N/A'}
                                                            </div>
                                                        </td>
                                                        <td className="px-4 py-2 whitespace-nowrap max-w-xs">
                                                            <div className="text-sm text-gray-900 truncate" title={candidate.recruiter || 'N/A'}>
                                                                {candidate.recruiter || 'N/A'}
                                                            </div>
                                                        </td>
                                                        <td className="px-4 py-2 whitespace-nowrap">
                                                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusStyling.badge}`}>
                                                                <StatusIcon className={`h-4 w-4 mr-1.5 ${statusStyling.iconColor}`} />
                                                                {candidate.status || 'N/A'}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                );
                                            })
                                        ) : (
                                            <tr>
                                                <td colSpan={6} className="px-4 py-2 text-center text-sm text-gray-500">
                                                    No candidates found for this job.
                                                </td>
                                            </tr>
                                        )}
                                    </tbody>
                                </table>
                            </div>
                        </AnimatedTableWrapper>
                    )}

                    {/* Pagination */}
                    {totalPages > 1 && (
                        <div className="px-4 py-2 border-t border-gray-200">
                            <div className="flex items-center justify-between">
                                <div className="text-sm text-gray-700">
                                    Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredCandidates.length)} of {filteredCandidates.length} candidates
                                </div>
                                <AnimatedPagination
                                    currentPage={currentPage}
                                    totalPages={totalPages}
                                    onPageChange={handlePageChange}
                                />
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}
