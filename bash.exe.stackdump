Stack trace:
Frame         Function      Args
0007FFFF9640  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8540) msys-2.0.dll+0x2118E
0007FFFF9640  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF9640  0002100469F2 (00021028DF99, 0007FFFF94F8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9640  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9640  00021006A545 (0007FFFF9650, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF9650, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFEB6040000 ntdll.dll
7FFEB5EE0000 KERNEL32.DLL
7FFEB35E0000 KERNELBASE.dll
7FFEB3EC0000 USER32.dll
7FFEB31D0000 win32u.dll
000210040000 msys-2.0.dll
7FFEB5B40000 GDI32.dll
7FFEB34B0000 gdi32full.dll
7FFEB3200000 msvcp_win.dll
7FFEB39E0000 ucrtbase.dll
7FFEB5B70000 advapi32.dll
7FFEB5CC0000 msvcrt.dll
7FFEB3E10000 sechost.dll
7FFEB4770000 RPCRT4.dll
7FFEB27F0000 CRYPTBASE.DLL
7FFEB3D70000 bcryptPrimitives.dll
7FFEB4170000 IMM32.DLL
