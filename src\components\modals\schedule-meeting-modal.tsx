// ScheduleMeetingModal.tsx

import { useState, useEffect } from "react";
import { X, FileText, Users, Calendar, Clock, Globe, Paperclip, Video, Loader2, Plus, Minus, CheckCircle, Sparkles } from "lucide-react";
import { type Candidate } from "@/types/candidate";
import { toast } from "react-toastify";
import { ApiService, type TeamsMeetingRequest, type ZoomMeetingRequest } from "@/services/api";

interface ScheduleMeetingModalProps {
  candidate: Candidate | null;
  isOpen: boolean;
  onClose: () => void;
  meetingType?: "Teams" | "Zoom";
}

// Utility for base64
const fileToBase64 = async (file: File): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () =>
      resolve((reader.result as string).split(",")[1] ?? "");
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });

// Skeleton Loader Component for ScheduleMeetingModal
const SkeletonLoader = () => (
  <div className="animate-pulse space-y-6">
    <div className="space-y-3">
      <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-32"></div>
      <div className="h-12 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded-lg"></div>
    </div>
    <div className="space-y-3">
      <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-24"></div>
      <div className="h-12 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded-lg"></div>
    </div>
    <div className="space-y-3">
      <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-28"></div>
      <div className="flex gap-2 mb-2">
        <div className="h-8 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded-full w-32"></div>
        <div className="h-8 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded-full w-24"></div>
      </div>
      <div className="h-12 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded-lg"></div>
    </div>
    <div className="space-y-3">
      <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-40"></div>
      <div className="h-24 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded-lg"></div>
    </div>
    <div className="grid grid-cols-2 gap-4">
      <div className="space-y-3">
        <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-20"></div>
        <div className="h-12 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded-lg"></div>
      </div>
      <div className="space-y-3">
        <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-18"></div>
        <div className="h-12 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded-lg"></div>
      </div>
    </div>
    <div className="space-y-3">
      <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-22"></div>
      <div className="h-12 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded-lg"></div>
    </div>
  </div>
);

export function ScheduleMeetingModal({
  candidate,
  isOpen,
  onClose,
  meetingType,
}: ScheduleMeetingModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [formData, setFormData] = useState({
    title: "",
    attendees: [] as string[],
    optionalAttendees: [] as string[],
    description: "",
    startDate: "",
    endDate: "",
    timeZone: "UTC",
    startTime: "",
    endTime: "",
    attachments: [] as File[],
    meetingType: meetingType || "Teams",
  });

  const [newAttendee, setNewAttendee] = useState("");
  const [newOptionalAttendee, setNewOptionalAttendee] = useState("");

  useEffect(() => {
    if (isOpen && candidate) {
      // Check if candidate has selected date from calendar
      const selectedDate = (candidate as any).selectedDate;
      let startDate;

      if (selectedDate && selectedDate instanceof Date) {
        // Ensure we're working with the local date, not UTC
        const year = selectedDate.getFullYear();
        const month = String(selectedDate.getMonth() + 1).padStart(2, '0');
        const day = String(selectedDate.getDate()).padStart(2, '0');
        startDate = `${year}-${month}-${day}`;
      } else {
        startDate = new Date().toISOString().split("T")[0];
      }

      console.log('Calendar selected date:', selectedDate);
      console.log('Form start date:', startDate);

      setFormData((prev) => ({
        ...prev,
        title: `Meeting | ${candidate.client} | ${candidate.profile} | Internal Screening`,
        attendees: [candidate.email],
        startDate: startDate,
        endDate: startDate,
        timeZone: "UTC",
        meetingType: meetingType || "Teams",
      }));
    }
  }, [isOpen, candidate, meetingType]);

  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    let previousOverflow = "";
    if (isOpen) {
      document.addEventListener("keydown", handleEscapeKey);
      previousOverflow = document.body.style.overflow;
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscapeKey);
      document.body.style.overflow = previousOverflow;
    };
  }, [isOpen, onClose]);

  if (!isOpen || !candidate) return null;

  const addAttendee = () => {
    if (
      newAttendee.trim() &&
      !formData.attendees.includes(newAttendee.trim())
    ) {
      setFormData((prev) => ({
        ...prev,
        attendees: [...prev.attendees, newAttendee.trim()],
      }));
      setNewAttendee("");
    }
  };

  const removeAttendee = (email: string) => {
    setFormData((prev) => ({
      ...prev,
      attendees: prev.attendees.filter((a) => a !== email),
    }));
  };

  const addOptionalAttendee = () => {
    if (
      newOptionalAttendee.trim() &&
      !formData.optionalAttendees.includes(newOptionalAttendee.trim())
    ) {
      setFormData((prev) => ({
        ...prev,
        optionalAttendees: [
          ...prev.optionalAttendees,
          newOptionalAttendee.trim(),
        ],
      }));
      setNewOptionalAttendee("");
    }
  };

  const removeOptionalAttendee = (email: string) => {
    setFormData((prev) => ({
      ...prev,
      optionalAttendees: prev.optionalAttendees.filter((a) => a !== email),
    }));
  };

  const addAttachment = (file: File) => {
    if (file && !formData.attachments.some(f => f.name === file.name && f.size === file.size)) {
      setFormData((prev) => ({
        ...prev,
        attachments: [...prev.attachments, file],
      }));
    }
  };

  const removeAttachment = (fileName: string) => {
    setFormData((prev) => ({
      ...prev,
      attachments: prev.attachments.filter((f) => f.name !== fileName),
    }));
  };

  // Form validation
  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.title.trim()) errors.title = 'Title is required';

    // Start date validation
    if (!formData.startDate) {
      errors.startDate = 'Start date is required';
    } else {
      const selectedStartDate = new Date(formData.startDate);
      const today = new Date();

      // Reset time to compare only dates
      selectedStartDate.setHours(0, 0, 0, 0);
      today.setHours(0, 0, 0, 0);

      if (selectedStartDate < today) {
        errors.startDate = 'Start date cannot be in the past';
      }
    }

    // End date validation
    if (!formData.endDate) {
      errors.endDate = 'End date is required';
    } else if (formData.startDate) {
      const selectedStartDate = new Date(formData.startDate);
      const selectedEndDate = new Date(formData.endDate);
      const today = new Date();
      const tomorrow = new Date(today);
      tomorrow.setDate(today.getDate() + 1);

      // Reset time to compare only dates
      selectedStartDate.setHours(0, 0, 0, 0);
      selectedEndDate.setHours(0, 0, 0, 0);
      today.setHours(0, 0, 0, 0);
      tomorrow.setHours(0, 0, 0, 0);

      if (selectedEndDate < today) {
        errors.endDate = 'End date cannot be in the past';
      } else if (selectedEndDate < selectedStartDate) {
        errors.endDate = 'End date cannot be before start date';
      } else if (selectedEndDate > tomorrow) {
        errors.endDate = 'End date can only be today or tomorrow';
      }
    }

    if (!formData.startTime) errors.startTime = 'Start time is required';
    if (!formData.endTime) errors.endTime = 'End time is required';
    if (!formData.attendees.length) errors.attendees = 'At least one attendee is required';
    if (!formData.meetingType) errors.meetingType = 'Meeting type is required';

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleSave = async () => {
    if (isSubmitting) return; // Prevent multiple submissions

    if (!validateForm()) {
      toast.error('Please fix the errors and try again.');
      return;
    }

    setIsSubmitting(true);
    setFormErrors({});

    try {
      const {
        title, attendees, optionalAttendees, description,
        startDate, endDate, timeZone, startTime, endTime, attachments, meetingType
      } = formData;

      if (meetingType === "Teams") {
        // For Teams meetings, use the API service
        const teamsMeetingData: TeamsMeetingRequest = {
          subject: title,
          attendees: attendees,
          cc_recipients: optionalAttendees,
          recruiter_email: localStorage.getItem('userEmail') || '',
          start_date: startDate,
          end_date: endDate,
          start_time: startTime,
          end_time: endTime,
          time_zone: timeZone,
          description: description,
          files: attachments.length > 0 ? await Promise.all(
            attachments.map(async (file) => ({
              file_name: file.name,
              file_content_base64: await fileToBase64(file)
            }))
          ) : undefined,
        };

        await ApiService.createTeamsMeeting(teamsMeetingData);


      } else {
        // For Zoom, use the API service
        const zoomMeetingData: ZoomMeetingRequest = {
          subject: title,
          description: description,
          start_date: startDate,
          start_time: startTime,
          end_time: endTime,
          timezone: timeZone,
          recruiter_email: localStorage.getItem('userEmail') || '',
          recruiter_id: parseInt(localStorage.getItem('userId') || '0'),
          attendees: attendees,
          cc_recipients: optionalAttendees,
        };

        if (attachments.length > 0) {
          zoomMeetingData.files = await Promise.all(
            attachments.map(async (file) => ({
              file_name: file.name,
              file_content_base64: await fileToBase64(file),
            }))
          );
        }

        await ApiService.createZoomMeeting(zoomMeetingData);
      }

      setIsSuccess(true);
      toast.success("Meeting scheduled successfully!");

      // Show success animation for 2 seconds before closing
      setTimeout(() => {
        setIsSuccess(false);
        onClose();
      }, 2000);
    } catch (err: any) {
      toast.error(err?.message || "Failed to schedule meeting. Please try again.");
    } finally {
      if (!isSuccess) {
        setIsSubmitting(false);
      }
    }
  };

  return (
    <div
      className={`fixed inset-0 flex items-center justify-center z-50 p-4 transition-all duration-500 ${isSuccess
        ? 'bg-gradient-to-br from-blue-400 via-indigo-500 to-purple-600 backdrop-blur-sm'
        : 'bg-gradient-to-br from-gray-900/80 via-gray-800/70 to-black/60 backdrop-blur-md'
        }`}
      onClick={handleBackdropClick}
    >
      <div className={`bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden transform transition-all duration-500 ${isOpen ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
        } ${isSuccess ? 'ring-4 ring-blue-300 ring-opacity-50' : ''}`}>
        {/* Enhanced Header with Gradient */}
        <div className={`relative overflow-hidden ${isSuccess
          ? 'bg-gradient-to-r from-blue-500 via-indigo-600 to-purple-600'
          : 'bg-gradient-to-r from-blue-500 via-indigo-600 to-purple-600'
          }`}>
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse"></div>
          <div className="relative p-3 text-white">
            <div className="flex items-center justify-center gap-3">
              <div className={`p-1 rounded-full ${isSuccess ? 'bg-white/20' : 'bg-white/10'} backdrop-blur-sm`}>
                {isSuccess ? (
                  <CheckCircle className="h-5 w-5 animate-bounce" />
                ) : (
                  <Video className="h-5 w-5" />
                )}
              </div>
              <div className="text-center">
                <h2 className="text-xl font-bold bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
                  {isSuccess ? 'Meeting Scheduled!' : 'New Meeting'}
                </h2>

              </div>
            </div>
          </div>
          {!isSuccess && (
            <button
              onClick={onClose}
              className="absolute top-4 right-4 text-white hover:text-blue-200 transition-all duration-200 p-2 rounded-full hover:bg-white/10"
            >
              <X className="h-5 w-5" />
            </button>
          )}
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)] bg-gradient-to-b from-gray-50 to-white">
          {isSuccess ? (
            <div className="text-center py-12 space-y-6">
              <div className="relative">
                <div className="w-24 h-24 mx-auto bg-gradient-to-r from-blue-400 to-indigo-500 rounded-full flex items-center justify-center animate-bounce">
                  <CheckCircle className="h-12 w-12 text-white" />
                </div>
                <div className="absolute -inset-4 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-full opacity-20 animate-ping"></div>
              </div>
              <div className="space-y-2">
                <h3 className="text-2xl font-bold text-gray-800">Success!</h3>
                <p className="text-gray-600">Your meeting has been scheduled successfully.</p>
                <div className="flex items-center justify-center gap-2 mt-4">
                  <Sparkles className="h-5 w-5 text-yellow-500 animate-spin" />
                  <span className="text-sm text-gray-500">Redirecting...</span>
                </div>
              </div>
            </div>
          ) : isSubmitting ? (
            <SkeletonLoader />
          ) : (
            <div className="space-y-6 animate-in fade-in duration-300">

              {/* Meeting Type */}
              <div className="space-y-1">
                <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                  <Video className="h-4 w-4 mr-2 text-blue-600" />
                  Meeting Type *
                </label>
                <select
                  value={formData.meetingType}
                  onChange={e => handleInputChange('meetingType', e.target.value)}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                >
                  <option value="Teams">Microsoft Teams</option>
                  <option value="Zoom">Zoom</option>
                </select>
              </div>

              {/* Title */}
              <div className="space-y-1">
                <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                  <FileText className="h-4 w-4 mr-2 text-blue-600" />
                  Title *
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    className="w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                    placeholder="Enter meeting title"
                  />
                  <FileText className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 pointer-events-none" />
                </div>
              </div>

              {/* Attendees */}
              <div className="space-y-1">
                <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                  <Users className="h-4 w-4 mr-2 text-blue-600" />
                  Attendees *
                </label>
                <div className="space-y-3">
                  <div className="flex flex-wrap gap-2 mb-2">
                    {formData.attendees.map((email) => (
                      <span
                        key={email}
                        className="inline-flex items-center px-3 py-2 rounded-full text-sm bg-blue-100 text-blue-800 border border-blue-200 transition-all duration-200 hover:bg-blue-200"
                      >
                        {email}
                        <button
                          onClick={() => removeAttendee(email)}
                          className="ml-2 text-blue-600 hover:text-blue-800 p-0.5 rounded-full hover:bg-blue-300 transition-colors"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                  <div className="flex gap-2">
                    <div className="relative flex-1">
                      <input
                        type="email"
                        value={newAttendee}
                        onChange={(e) => setNewAttendee(e.target.value)}
                        onKeyPress={(e) => e.key === "Enter" && addAttendee()}
                        placeholder="Search or add email"
                        className="w-full pl-3 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                      />
                    </div>
                    <button
                      onClick={addAttendee}
                      className="px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-200 flex items-center gap-2"
                    >
                      <Plus className="h-4 w-4" />
                      Add
                    </button>
                  </div>
                </div>
              </div>

              {/* Optional Attendees */}
              <div className="space-y-1">
                <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                  <Users className="h-4 w-4 mr-2 text-gray-600" />
                  Optional Attendees
                </label>
                <div className="space-y-3">
                  <div className="flex flex-wrap gap-2 mb-2">
                    {formData.optionalAttendees.map((email) => (
                      <span
                        key={email}
                        className="inline-flex items-center px-3 py-2 rounded-full text-sm bg-gray-100 text-gray-800 border border-gray-200 transition-all duration-200 hover:bg-gray-200"
                      >
                        {email}
                        <button
                          onClick={() => removeOptionalAttendee(email)}
                          className="ml-2 text-gray-600 hover:text-gray-800 p-0.5 rounded-full hover:bg-gray-300 transition-colors"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                  <div className="flex gap-2">
                    <div className="relative flex-1">
                      <input
                        type="email"
                        value={newOptionalAttendee}
                        onChange={(e) => setNewOptionalAttendee(e.target.value)}
                        onKeyPress={(e) =>
                          e.key === "Enter" && addOptionalAttendee()
                        }
                        placeholder="Search or add email"
                        className="w-full pl-3 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                      />
                    </div>
                    <button
                      onClick={addOptionalAttendee}
                      className="px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-all duration-200 flex items-center gap-2"
                    >
                      <Plus className="h-4 w-4" />
                      Add
                    </button>
                  </div>
                </div>
              </div>

              {/* Description */}
              <div className="space-y-1">
                <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                  <FileText className="h-4 w-4 mr-2 text-blue-600" />
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  placeholder="Enter meeting description"
                  rows={3}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white resize-none"
                />
              </div>

              {/* Date Range */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                    <Calendar className="h-4 w-4 mr-2 text-blue-600" />
                    Start Date*
                  </label>
                  <input
                    type="date"
                    value={formData.startDate}
                    min={new Date().toISOString().split('T')[0]}
                    max={new Date(new Date().setDate(new Date().getDate() + 1)).toISOString().split('T')[0]}
                    onChange={(e) => {
                      const newStartDate = e.target.value;
                      setFormData((prev) => {
                        let newEndDate = prev.endDate;

                        // If we have a start date, ensure end date is valid
                        if (newStartDate) {
                          const startDate = new Date(newStartDate);
                          const today = new Date();
                          const tomorrow = new Date(today);
                          tomorrow.setDate(today.getDate() + 1);

                          startDate.setHours(0, 0, 0, 0);
                          today.setHours(0, 0, 0, 0);
                          tomorrow.setHours(0, 0, 0, 0);

                          // If current end date is invalid, set it to start date
                          if (prev.endDate) {
                            const endDate = new Date(prev.endDate);
                            endDate.setHours(0, 0, 0, 0);

                            if (endDate < startDate || endDate > tomorrow) {
                              newEndDate = newStartDate;
                            }
                          } else {
                            newEndDate = newStartDate;
                          }
                        }

                        return {
                          ...prev,
                          startDate: newStartDate,
                          endDate: newEndDate,
                        };
                      });
                    }}
                    className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                  />
                </div>
                <div className="space-y-1">
                  <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                    <Calendar className="h-4 w-4 mr-2 text-blue-600" />
                    End Date*
                  </label>
                  <input
                    type="date"
                    value={formData.endDate}
                    min={formData.startDate || new Date().toISOString().split('T')[0]}
                    max={new Date(new Date().setDate(new Date().getDate() + 1)).toISOString().split('T')[0]}
                    onChange={(e) => {
                      const newEndDate = e.target.value;
                      handleInputChange('endDate', newEndDate);
                    }}
                    className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                  />
                </div>
              </div>

              {/* Time Zone */}
              <div className="space-y-1">
                <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                  <Globe className="h-4 w-4 mr-2 text-blue-600" />
                  Time Zone
                </label>
                <select
                  value={formData.timeZone}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, timeZone: e.target.value }))
                  }
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                >
                  <option value="">Select a time zone</option>
                  <option value="UTC">UTC</option>
                  <option value="EST">Eastern Standard Time</option>
                  <option value="PST">Pacific Standard Time</option>
                  <option value="IST">India Standard Time</option>
                  <option value="Asia/Kolkata">Asia/Kolkata</option>
                </select>
              </div>

              {/* Time Range */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                    <Clock className="h-4 w-4 mr-2 text-blue-600" />
                    Start Time*
                  </label>
                  <input
                    type="time"
                    value={formData.startTime}
                    onChange={(e) => {
                      const startTime = e.target.value;
                      // Calculate end time 30 minutes after start time
                      let endTime = '';
                      if (startTime) {
                        const [hours, minutes] = startTime.split(':').map(Number);
                        const startDate = new Date();
                        startDate.setHours(hours, minutes, 0, 0);
                        startDate.setMinutes(startDate.getMinutes() + 30);
                        endTime = startDate.toTimeString().slice(0, 5);
                      }

                      setFormData((prev) => ({
                        ...prev,
                        startTime: startTime,
                        endTime: endTime,
                      }));
                    }}
                    className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                  />
                </div>
                <div className="space-y-1">
                  <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                    <Clock className="h-4 w-4 mr-2 text-blue-600" />
                    End Time*
                  </label>
                  <input
                    type="time"
                    value={formData.endTime}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        endTime: e.target.value,
                      }))
                    }
                    className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                  />
                </div>
              </div>

              {/* Attachments */}
              <div className="space-y-1">
                <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                  <Paperclip className="h-4 w-4 mr-2 text-blue-600" />
                  Attachments
                </label>
                <input
                  type="file"
                  multiple
                  onChange={(e) => {
                    const files = Array.from(e.target.files || []);
                    files.forEach(file => addAttachment(file));
                  }}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                />

                {/* Display selected files */}
                {formData.attachments.length > 0 && (
                  <div className="mt-4 space-y-3">
                    <p className="text-sm font-medium text-gray-700 flex items-center gap-2">
                      <Paperclip className="h-4 w-4" />
                      Selected Files ({formData.attachments.length})
                    </p>
                    {formData.attachments.map((file, index) => (
                      <div key={index} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg border border-gray-200">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-blue-100 rounded-md">
                            <Paperclip className="h-4 w-4 text-blue-600" />
                          </div>
                          <div>
                            <span className="text-sm font-medium text-gray-900">{file.name}</span>
                            <p className="text-xs text-gray-500">{(file.size / 1024).toFixed(1)} KB</p>
                          </div>
                        </div>
                        <button
                          type="button"
                          onClick={() => removeAttachment(file.name)}
                          className="text-red-500 hover:text-red-700 p-2 rounded-md hover:bg-red-50 transition-colors"
                        >
                          <Minus className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-6 py-3 text-gray-700 bg-gradient-to-r from-gray-100 to-gray-200 rounded-lg hover:from-gray-200 hover:to-gray-300 transition-all duration-200 font-medium shadow-sm hover:shadow-md transform hover:-translate-y-0.5"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={isSubmitting}
            className="px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-lg hover:from-blue-600 hover:to-indigo-700 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 min-w-[180px] justify-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none disabled:hover:shadow-lg"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="h-5 w-5 animate-spin" />
                <span className="animate-pulse">Scheduling...</span>
              </>
            ) : (
              <>
                <Video className="h-5 w-5" />
                <span>Schedule Meeting</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
