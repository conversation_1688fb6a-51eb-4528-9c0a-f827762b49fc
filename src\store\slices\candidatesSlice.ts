import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { ApiService, convertApiCandidateToLocal, type ApiResponse, type CheckCandidateRequest, type AddCandidateRequest } from '@/services/api';
import { type Candidate } from '@/types/candidate';

// Filter tag interface
export interface FilterTag {
  column: string;
  value: string;
}

// Candidates state interface
export interface CandidatesState {
  candidates: Candidate[];
  loading: boolean;
  error: string | null;
  lastFetched: string | null;
  apiResponse: ApiResponse | null;
  searchTags: FilterTag[];
  appliedTags: FilterTag[];
  dateFilter: number | null;
  appliedCustomDateRange: { start: string; end: string } | null;
  currentPage: number;
  itemsPerPage: number;
  sortConfig: {
    key: keyof Candidate | null;
    direction: "ascending" | "descending" | null;
  };
  // UI state
  selectedRows: number[];
  selectAll: boolean;
  // Column visibility
  visibleColumns: (keyof Candidate)[];
}

// Initial state
const initialState: CandidatesState = {
  candidates: [],
  loading: false,
  error: null,
  lastFetched: null,
  apiResponse: null,
  searchTags: [],
  appliedTags: [],
  dateFilter: null,
  appliedCustomDateRange: null,
  currentPage: 1,
  itemsPerPage: 10,
  sortConfig: { key: null, direction: null },
  selectedRows: [],
  selectAll: false,
  visibleColumns: [
    'appliedDate',
    'lastUpdated',
    'jobId',
    'firstName',
    'email',
    'phone',
    'client',
    'profile',
    'skills',
    'status',
    'comment',
    'peerReviewer',
    'recruiter'
  ],
};

// Async thunk for fetching candidates
export const fetchCandidates = createAsyncThunk(
  'candidates/fetchCandidates',
  async (params: { userId: string; userType: string; userName: string; force?: boolean }, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { candidates: CandidatesState };
      const { lastFetched, candidates } = state.candidates;

      // Check cache validity (5 minutes)
      const CACHE_DURATION = 5 * 60 * 1000;
      if (lastFetched && Date.now() - new Date(lastFetched).getTime() < CACHE_DURATION) {
        // Using cached candidates data
        return { candidates, fromCache: true };
      }

      // Fetching candidates from API...
      const response = await ApiService.fetchCandidates(
        params.userId,
        params.userType,
        params.userName,
        1 // For now, always fetch page 1
      );

      // Convert API candidates to local format
      const convertedCandidates = response.candidates.map((candidate, index) => {
        try {
          return convertApiCandidateToLocal(candidate);
        } catch (error) {
          console.error(`Error converting candidate at index ${index}:`, error, candidate);
          // Return a fallback candidate object
          return {
            id: candidate.id || index,
            jobId: "N/A",
            firstName: "Error",
            lastName: "Loading",
            email: "",
            phone: "",
            client: "N/A",
            profile: "N/A",
            skills: "",
            status: "Error",
            appliedDate: new Date().toISOString(),
            source: "API",
            experience: 0,
            education: "",
            location: "",
            salary: "N/A - N/A",
            notes: "",
            lastUpdated: new Date().toISOString(),
            comment: "",
            peerReviewer: "",
            recruiter: "",
            management: null,
            client_assigned: false,
          };
        }
      });

      return {
        candidates: convertedCandidates,
        apiResponse: response,
        fromCache: false
      };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : "An unknown error occurred");
    }
  }
);

// Async thunk for checking candidate duplicates
export const checkCandidate = createAsyncThunk(
  'candidates/checkCandidate',
  async (candidateData: CheckCandidateRequest, { rejectWithValue }) => {
    try {
      const response = await ApiService.checkCandidate(candidateData);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : "Failed to check candidate");
    }
  }
);

// Async thunk for adding new candidate
export const addCandidate = createAsyncThunk(
  'candidates/addCandidate',
  async (candidateData: AddCandidateRequest, { rejectWithValue }) => {
    try {
      const response = await ApiService.addCandidate(candidateData);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : "Failed to add candidate");
    }
  }
);

// Candidates slice
const candidatesSlice = createSlice({
  name: 'candidates',
  initialState,
  reducers: {
    // Search and filter actions
    addSearchTag: (state, action: PayloadAction<FilterTag>) => {
      const newTag = action.payload;
      if (!state.searchTags.some(t => t.value === newTag.value && t.column === newTag.column)) {
        state.searchTags.push(newTag);
      }
    },
    removeSearchTag: (state, action: PayloadAction<FilterTag>) => {
      const tagToRemove = action.payload;
      state.searchTags = state.searchTags.filter(
        tag => tag.value !== tagToRemove.value || tag.column !== tagToRemove.column
      );
    },
    applyFilters: (state) => {
      state.appliedTags = [...state.searchTags];
      state.dateFilter = null;
      state.appliedCustomDateRange = null;
      state.currentPage = 1;
    },
    clearAllFilters: (state) => {
      state.searchTags = [];
      state.appliedTags = [];
      state.dateFilter = null;
      state.appliedCustomDateRange = null;
      state.currentPage = 1;
    },
    setDateFilter: (state, action: PayloadAction<number | null>) => {
      state.appliedTags = [];
      state.searchTags = [];
      state.dateFilter = action.payload;
      state.appliedCustomDateRange = null;
      state.currentPage = 1;
    },
    setCustomDateRange: (state, action: PayloadAction<{ start: string; end: string }>) => {
      state.appliedCustomDateRange = action.payload;
      state.dateFilter = null;
      state.appliedTags = [];
      state.searchTags = [];
      state.currentPage = 1;
    },
    // Pagination actions
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
    setItemsPerPage: (state, action: PayloadAction<number>) => {
      state.itemsPerPage = action.payload;
      state.currentPage = 1;
    },
    // Sorting actions
    setSortConfig: (state, action: PayloadAction<{
      key: keyof Candidate | null;
      direction: "ascending" | "descending" | null;
    }>) => {
      state.sortConfig = action.payload;
    },
    // Row selection actions
    toggleRowSelection: (state, action: PayloadAction<number>) => {
      const candidateId = action.payload;
      const index = state.selectedRows.indexOf(candidateId);
      if (index > -1) {
        state.selectedRows.splice(index, 1);
      } else {
        state.selectedRows.push(candidateId);
      }
      state.selectAll = state.selectedRows.length === state.candidates.length;
    },
    setSelectAll: (state, action: PayloadAction<boolean>) => {
      state.selectAll = action.payload;
      if (action.payload) {
        state.selectedRows = state.candidates.map(c => c.id);
      } else {
        state.selectedRows = [];
      }
    },
    clearSelection: (state) => {
      state.selectedRows = [];
      state.selectAll = false;
    },
    // Column visibility actions
    toggleColumnVisibility: (state, action: PayloadAction<keyof Candidate>) => {
      const column = action.payload;
      const index = state.visibleColumns.indexOf(column);
      if (index > -1) {
        state.visibleColumns.splice(index, 1);
      } else {
        state.visibleColumns.push(column);
      }
    },
    setVisibleColumns: (state, action: PayloadAction<(keyof Candidate)[]>) => {
      state.visibleColumns = action.payload;
    },
    // Cache management
    clearCache: (state) => {
      state.candidates = [];
      state.lastFetched = null;
      state.apiResponse = null;
      state.error = null;
    },
    refreshCandidates: (state) => {
      state.lastFetched = null; // This will force a refresh on next fetch
    },
    // Local updates without API re-fetch
    upsertCandidate: (state, action: PayloadAction<Candidate>) => {
      const updatedCandidate = action.payload;
      const existingIndex = state.candidates.findIndex(c => c.id === updatedCandidate.id);
      if (existingIndex >= 0) {
        state.candidates[existingIndex] = updatedCandidate;
      } else {
        state.candidates.push(updatedCandidate);
      }
      state.lastFetched = new Date().toISOString();
    },
    updateCandidateStatus: (state, action: PayloadAction<{ id: number; status: string; comment?: string }>) => {
      const { id, status, comment } = action.payload;
      const candidate = state.candidates.find(c => c.id === id);
      if (candidate) {
        candidate.status = status as any;
        if (comment !== undefined) {
          candidate.comment = comment;
        }
        candidate.lastUpdated = new Date().toISOString().split('T')[0];
      }
      state.lastFetched = new Date().toISOString();
    },
    removeCandidate: (state, action: PayloadAction<number>) => {
      const id = action.payload;
      state.candidates = state.candidates.filter(c => c.id !== id);
      state.selectedRows = state.selectedRows.filter(rowId => rowId !== id);
      state.selectAll = state.selectedRows.length === state.candidates.length && state.candidates.length > 0;
      state.lastFetched = new Date().toISOString();
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchCandidates.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCandidates.fulfilled, (state, action) => {
        state.loading = false;
        // Always update candidates, whether from cache or API
        state.candidates = action.payload.candidates;
        if (!action.payload.fromCache) {
          state.apiResponse = action.payload.apiResponse || null;
          state.lastFetched = new Date().toISOString();
        }
        state.error = null;
      })
      .addCase(fetchCandidates.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Check candidate cases
      .addCase(checkCandidate.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(checkCandidate.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(checkCandidate.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Add candidate cases
      .addCase(addCandidate.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addCandidate.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        // Add the new candidate to the list
        if (action.payload.candidate_id && action.payload.date_created) {
          // Create a new candidate object from the form data
          // This will be handled in the component
        }
      })
      .addCase(addCandidate.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  addSearchTag,
  removeSearchTag,
  applyFilters,
  clearAllFilters,
  setDateFilter,
  setCustomDateRange,
  setCurrentPage,
  setItemsPerPage,
  setSortConfig,
  toggleRowSelection,
  setSelectAll,
  clearSelection,
  toggleColumnVisibility,
  setVisibleColumns,
  clearCache,
  refreshCandidates,
  upsertCandidate,
  updateCandidateStatus,
  removeCandidate,
} = candidatesSlice.actions;

export default candidatesSlice.reducer;
