import CryptoJS from 'crypto-js';
import { config } from '@/config/env';

const API_BASE_URL = config.apiBaseUrl;
const SECRET_KEY = 'ATS@mako';

export interface ChangePasswordRequest {
  user_id: number | string;
  username: string;
  old_password: string;
  new_password: string;
  confirm_password: string;
}

export interface ChangePasswordResponse {
  status: string;
  message?: string;
}

export class ChangePasswordService {
  // Encrypt password using AES encryption
  private static encryptPassword(password: string): string {
    return CryptoJS.AES.encrypt(password, SECRET_KEY).toString();
  }

  static async changePassword(
    user_id: number | string,
    username: string,
    oldPassword: string,
    newPassword: string,
    confirmPassword: string
  ): Promise<ChangePasswordResponse> {
    const requestBody: ChangePasswordRequest = {
      user_id,
      username,
      old_password: this.encryptPassword(oldPassword),
      new_password: this.encryptPassword(newPassword),
      confirm_password: this.encryptPassword(confirmPassword),
    };

    const response = await fetch(`${API_BASE_URL}/change_password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }
}
