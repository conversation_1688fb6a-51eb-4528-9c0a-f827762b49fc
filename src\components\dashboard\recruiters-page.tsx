import { useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { Users, FileText, CheckCircle, XCircle, ArrowLeft, Search, Clock } from "lucide-react";
import { useAppSelector } from "@/store/hooks";
import { selectCandidates, selectCandidatesLoading } from "@/store/selectors/candidatesSelectors";
import { AnimatedPagination } from "@/components/ui/animated-pagination";

export function RecruitersPage() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<string | null>(null);

  const itemsPerPage = 5;

  // Get candidates data and loading state from Redux store
  const candidates = useAppSelector(selectCandidates);
  const candidatesLoading = useAppSelector(selectCandidatesLoading);

  // Extract unique recruiters from candidate data with performance metrics
  const recruitersData = useMemo(() => {
    if (!candidates || candidates.length === 0) return [];

    const recruiterMap = new Map<string, {
      id: string;
      name: string;
      submissions: number;
      selected: number;
      rejected: number;
      inProgress: number;
      successRate: number;
    }>();

    candidates.forEach(candidate => {
      const recruiterName = candidate.recruiter || candidate.management;
      if (recruiterName && recruiterName !== 'Not Assigned') {
        const displayName = candidate.management && !candidate.recruiter
          ? `${recruiterName} (Manager)`
          : recruiterName;

        if (recruiterMap.has(recruiterName)) {
          const existing = recruiterMap.get(recruiterName)!;
          existing.submissions++;

          // Simple 3-category system
          if (candidate.status === "Onboarded" || candidate.status === "Hired") {
            existing.selected++;
          } else if (candidate.status && (
            candidate.status.toLowerCase().includes('rejected') ||
            candidate.status.toLowerCase().includes('declined') ||
            candidate.status.toLowerCase().includes('dropped')
          )) {
            existing.rejected++;
          } else {
            // Everything else is In Progress
            existing.inProgress++;
          }
        } else {
          let selected = 0;
          let rejected = 0;
          let inProgress = 0;

          // Categorize first candidate
          if (candidate.status === "Onboarded" || candidate.status === "Hired") {
            selected = 1;
          } else if (candidate.status && (
            candidate.status.toLowerCase().includes('rejected') ||
            candidate.status.toLowerCase().includes('declined') ||
            candidate.status.toLowerCase().includes('dropped')
          )) {
            rejected = 1;
          } else {
            inProgress = 1;
          }

          recruiterMap.set(recruiterName, {
            id: recruiterName,
            name: displayName,
            submissions: 1,
            selected,
            rejected,
            inProgress,
            successRate: 0 // Will calculate after all candidates are processed
          });
        }
      }
    });

    // Calculate success rates and convert to array
    const recruitersArray = Array.from(recruiterMap.values()).map(recruiter => ({
      ...recruiter,
      successRate: recruiter.submissions > 0 ? Math.round((recruiter.selected / recruiter.submissions) * 100) : 0
    }));

    return recruitersArray.sort((a, b) => b.submissions - a.submissions);
  }, [candidates]);

  // Filter and pagination logic
  const filteredRecruiters = useMemo(() => {
    let filtered = recruitersData;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(recruiter =>
        recruiter.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (statusFilter) {
      filtered = filtered.filter(recruiter => {
        switch (statusFilter) {
          case 'submissions':
            return recruiter.submissions > 0;
          case 'selected':
            return recruiter.selected > 0;
          case 'rejected':
            return recruiter.rejected > 0;
          case 'inProgress':
            return recruiter.inProgress > 0;
          default:
            return true;
        }
      });
    }

    return filtered;
  }, [recruitersData, searchTerm, statusFilter]);

  const totalPages = Math.ceil(filteredRecruiters.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedRecruiters = filteredRecruiters.slice(startIndex, startIndex + itemsPerPage);

  // Reset to first page when search or filter changes
  useMemo(() => {
    setCurrentPage(1);
  }, [searchTerm, statusFilter]);

  // Calculate summary stats
  const totalRecruiters = recruitersData.length;
  const totalSubmissions = recruitersData.reduce((sum, recruiter) => sum + recruiter.submissions, 0);
  const totalSelected = recruitersData.reduce((sum, recruiter) => sum + recruiter.selected, 0);
  const totalRejected = recruitersData.reduce((sum, recruiter) => sum + recruiter.rejected, 0);
  const totalInProgress = recruitersData.reduce((sum, recruiter) => sum + recruiter.inProgress, 0);

  const handleBackToDashboard = () => {
    const userRole = localStorage.getItem('userRole') || 'manager';
    navigate(`/${userRole}/dashboard`);
  };

  const handleRecruiterClick = (recruiter: any) => {
    const userRole = localStorage.getItem('userRole') || 'manager';
    navigate(`/${userRole}/recruiters/${recruiter.id}`);
  };

  return (
    <div className="flex-1 p-6 bg-gray-50 overflow-y-auto h-full">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={handleBackToDashboard}
              className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Dashboard
            </button>

            <div className="relative">
              <Search className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search recruiters by name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          <h1 className="text-3xl font-bold text-gray-900">Recruiter Management</h1>
          <p className="text-gray-600 mt-2">
            Detailed overview of all your recruiters and their performance metrics. Click on the cards above to filter by status.
            {searchTerm && ` Showing ${filteredRecruiters.length} results for "${searchTerm}"`}
          </p>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
          <div
            className={`bg-white rounded-lg shadow-md p-6 border cursor-pointer transition-all hover:shadow-lg ${statusFilter === null ? 'border-blue-200 bg-blue-50' : 'border-gray-200'
              }`}
            onClick={() => setStatusFilter(null)}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Recruiters</p>
                <p className="text-3xl font-bold text-green-600">{totalRecruiters}</p>
              </div>
              <div className="p-3 rounded-full bg-green-500">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>

          <div
            className={`bg-white rounded-lg shadow-md p-6 border cursor-pointer transition-all hover:shadow-lg ${statusFilter === 'submissions' ? 'border-blue-200 bg-blue-50' : 'border-gray-200'
              }`}
            onClick={() => setStatusFilter('submissions')}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Submissions</p>
                <p className="text-3xl font-bold text-blue-600">{totalSubmissions}</p>
              </div>
              <div className="p-3 rounded-full bg-blue-500">
                <FileText className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>

          <div
            className={`bg-white rounded-lg shadow-md p-6 border cursor-pointer transition-all hover:shadow-lg ${statusFilter === 'selected' ? 'border-green-200 bg-green-50' : 'border-gray-200'
              }`}
            onClick={() => setStatusFilter('selected')}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Selected</p>
                <p className="text-3xl font-bold text-green-600">{totalSelected}</p>
              </div>
              <div className="p-3 rounded-full bg-green-500">
                <CheckCircle className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>

          <div
            className={`bg-white rounded-lg shadow-md p-6 border cursor-pointer transition-all hover:shadow-lg ${statusFilter === 'rejected' ? 'border-red-200 bg-red-50' : 'border-gray-200'
              }`}
            onClick={() => setStatusFilter('rejected')}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Rejected</p>
                <p className="text-3xl font-bold text-red-600">{totalRejected}</p>
              </div>
              <div className="p-3 rounded-full bg-red-500">
                <XCircle className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>

          <div
            className={`bg-white rounded-lg shadow-md p-6 border cursor-pointer transition-all hover:shadow-lg ${statusFilter === 'inProgress' ? 'border-yellow-200 bg-yellow-50' : 'border-gray-200'
              }`}
            onClick={() => setStatusFilter('inProgress')}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">In Progress</p>
                <p className="text-3xl font-bold text-yellow-600">{totalInProgress}</p>
              </div>
              <div className="p-3 rounded-full bg-yellow-500">
                <Clock className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>

        {/* Filter Status Display */}
        {statusFilter && (
          <div className="mb-6 flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">Filtered by:</span>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${statusFilter === 'submissions' ? 'bg-blue-100 text-blue-800' :
                statusFilter === 'selected' ? 'bg-green-100 text-green-800' :
                  statusFilter === 'rejected' ? 'bg-red-100 text-red-800' :
                    statusFilter === 'inProgress' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                }`}>
                {statusFilter === 'submissions' ? 'All Submissions' :
                  statusFilter === 'selected' ? 'Selected Candidates' :
                    statusFilter === 'rejected' ? 'Rejected Candidates' :
                      statusFilter === 'inProgress' ? 'In Progress Candidates' :
                        statusFilter}
              </span>
            </div>
            <button
              onClick={() => setStatusFilter(null)}
              className="text-sm text-blue-600 hover:text-blue-700 underline"
            >
              Clear Filter
            </button>
          </div>
        )}

        {/* Recruiters Table */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">All Recruiters</h2>
            <p className="text-gray-600 mt-1">
              {searchTerm && ` Showing ${filteredRecruiters.length} results for "${searchTerm}"`}
              {statusFilter && ` • Filtered by ${statusFilter === 'submissions' ? 'All Submissions' :
                statusFilter === 'selected' ? 'Selected Candidates' :
                  statusFilter === 'rejected' ? 'Rejected Candidates' :
                    statusFilter === 'inProgress' ? 'In Progress Candidates' : statusFilter}`}
              {!searchTerm && !statusFilter && `Showing all ${filteredRecruiters.length} recruiters`}
            </p>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recruiter Name</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Submissions</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Selected</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rejected</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">In Progress</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Success Rate</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {candidatesLoading ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-8 text-center">
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <span className="ml-2 text-gray-500">Loading recruiters data...</span>
                      </div>
                    </td>
                  </tr>
                ) : paginatedRecruiters.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-8 text-center text-gray-500">
                      {searchTerm || statusFilter ?
                        `No recruiters found matching the current filters` :
                        "No recruiters found"}
                    </td>
                  </tr>
                ) : (
                  paginatedRecruiters.map((recruiter) => (
                    <tr
                      key={recruiter.id}
                      className="hover:bg-gray-50 cursor-pointer transition-colors"
                      onClick={() => handleRecruiterClick(recruiter)}
                    >
                      <td className="px-2 py-1 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                            <span className="text-green-600 font-medium text-sm">
                              {recruiter.name.split(' ').map(word => word[0]).join('').substring(0, 2)}
                            </span>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{recruiter.name}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{recruiter.submissions}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex items-center">
                          <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                          {recruiter.selected}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex items-center">
                          <XCircle className="h-4 w-4 text-red-500 mr-1" />
                          {recruiter.rejected}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 text-yellow-500 mr-1" />
                          {recruiter.inProgress}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${recruiter.successRate >= 80 ? 'bg-green-100 text-green-800' :
                          recruiter.successRate >= 60 ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                          {recruiter.successRate}%
                        </span>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="px-6 py-4 border-t border-gray-200">
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 bg-white p-2 rounded-md border border-gray-200">
                <div className="text-xs sm:text-sm text-gray-600">
                  {filteredRecruiters.length > 0 ? (
                    <>
                      Showing{" "}
                      {Math.min(
                        1 + (currentPage - 1) * itemsPerPage,
                        filteredRecruiters.length
                      )}{" "}
                      to{" "}
                      {Math.min(currentPage * itemsPerPage, filteredRecruiters.length)}{" "}
                      of {filteredRecruiters.length} recruiters
                    </>
                  ) : (
                    "No recruiters found"
                  )}
                </div>
                <div className="w-full sm:w-auto flex justify-center sm:justify-end">
                  <AnimatedPagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={setCurrentPage}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
