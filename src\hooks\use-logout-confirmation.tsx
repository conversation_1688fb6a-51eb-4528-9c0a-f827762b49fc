import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useUser } from "@/contexts/user-context";
import { clearAllStores } from "@/store/actions";
import { LogoutConfirmationDialog } from "@/components/ui/logout-confirmation-dialog";

export function useLogoutConfirmation() {
  const [showLogoutDialog, setShowLogoutDialog] = useState(false);
  const navigate = useNavigate();
  const { logout } = useUser();

  const handleLogoutClick = () => {
    setShowLogoutDialog(true);
  };

  const handleLogoutConfirm = () => {
    // Close the dialog first
    setShowLogoutDialog(false);

    // Clear all stores and logout
    clearAllStores();
    logout();

    // Force clear any remaining localStorage items that might persist
    const keysToRemove = [
      "isAuthenticated", "userRole", "userName", "userEmail", "userId",
      "authToken", "name", "peerStatus", "recruiterTargets",
      "rememberedUsername", "user_id", "raise_issue_images", "username", "email"
    ];

    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
      sessionStorage.removeItem(key);
    });

    // Clear any recruiter-specific targets
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (
        key.startsWith('recruiterTargets_') ||
        key.startsWith('user_avatar_') ||
        key.includes('recruiter') ||
        key.includes('target') ||
        key.includes('user') ||
        key.includes('profile') ||
        key.includes('candidate') ||
        key.includes('job') ||
        key.includes('client')
      )) {
        localStorage.removeItem(key);
      }
    }

    // Clear any remaining cookies
    document.cookie.split(";").forEach(cookie => {
      const eqPos = cookie.indexOf("=");
      const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
      document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;
    });

    // Navigate to login page with replace to prevent back navigation
    navigate("/login", { replace: true });

    // Force a complete page refresh to ensure clean state
    setTimeout(() => {
      // Clear browser history and force reload
      if (window.history && window.history.pushState) {
        window.history.pushState(null, '', '/login');
      }

      // Clear any potential memory leaks
      if (window.gc) {
        window.gc();
      }

      // Force reload with cache clearing
      window.location.href = '/login';
      window.location.reload();
    }, 100);
  };

  const handleLogoutCancel = () => {
    setShowLogoutDialog(false);
  };

  const LogoutDialog = () => (
    <LogoutConfirmationDialog
      isOpen={showLogoutDialog}
      onConfirm={handleLogoutConfirm}
      onCancel={handleLogoutCancel}
    />
  );

  return {
    handleLogoutClick,
    LogoutDialog,
  };
}
