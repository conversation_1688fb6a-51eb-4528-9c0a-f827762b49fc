import { useState } from "react";
import { useUser } from "@/contexts/user-context";
import { ProfilePictureModal } from "@/components/modals/ProfilePictureModal";
import { RoleBadge } from "@/components/ui/role-badge";
import { toast } from "react-toastify";
import { ApiService } from "@/services/api";

export function ProfileAvatar() {
  const [isProfilePictureModalOpen, setIsProfilePictureModalOpen] = useState(false);


  const { userName, userRole, userAvatar, updateUserAvatar } = useUser();

  const handleChangeProfilePicture = () => {
    setIsProfilePictureModalOpen(true);
  };

  const handleSaveProfilePicture = async (imageData: string, filename?: string) => {
    const userId = localStorage.getItem("userId");
    if (!userId) {
      throw new Error("User ID not found");
    }

    // Store previous avatar for rollback on error
    const previousAvatar = userAvatar;

    try {
      // Optimistic update - update UI immediately using context
      updateUserAvatar(imageData === "" ? "" : imageData);

      // Make API call
      await ApiService.uploadUserImage(userId, imageData, filename);

      // Success - the optimistic update is already in place
      toast.success("Profile picture updated successfully");
    } catch (error) {
      // Rollback optimistic update on error using context
      updateUserAvatar(previousAvatar || "");
      toast.error("Failed to save profile picture");
      throw error;
    }
  };

  return (
    <>
      <div className="p-4 border-b border-[var(--sidebar-border)]">
        <div className="flex items-center space-x-3">
          <div className="relative">
            <button
              onClick={handleChangeProfilePicture}
              className="cursor-pointer hover:opacity-80 transition-opacity duration-200"
            >
              {userAvatar ? (
                <img
                  src={userAvatar}
                  alt="Profile"
                  className="w-10 h-10 rounded-full border-2 border-white/20 object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = "/default-avatar.png";
                  }}
                />
              ) : (
                <div className="w-10 h-10 rounded-full bg-gray-700 border-2 border-gray-600 flex items-center justify-center">
                  <span className="text-gray-300 font-semibold text-lg">
                    {userName ? userName.charAt(0).toUpperCase() : "U"}
                  </span>
                </div>
              )}
            </button>
          </div>

          <div className="flex flex-col">
            <p className="text-sm font-bold text-white leading-tight">
              {userName || "User Name"}
            </p>
            <div className="flex items-center gap-2">
              <p className="text-xs text-gray-300 capitalize leading-tight">
                {userRole || "User"}
              </p>
              <RoleBadge />
            </div>
          </div>
        </div>
      </div>

      {/* Profile Picture Modal */}
      <ProfilePictureModal
        isOpen={isProfilePictureModalOpen}
        onClose={() => setIsProfilePictureModalOpen(false)}
        onSave={handleSaveProfilePicture}
        currentImage={userAvatar}
        userName={userName}
      />
    </>
  );
}
