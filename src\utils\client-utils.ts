import { Job } from '@/store/slices/jobsSlice';

/**
 * Utility functions for consistent client-related calculations across the application
 * This ensures dashboard and client management show the same numbers
 */

export interface ClientSummary {
    totalClients: number;
    activeClients: number;
    holdClients: number;
    closedClients: number;
    totalJobs: number;
    activeJobs: number;
    holdJobs: number;
    closedJobs: number;
}

/**
 * Calculate client summary from jobs data
 * This is the single source of truth for client calculations
 */
export const calculateClientSummary = (jobs: Job[]): ClientSummary => {
    const clientMap = new Map<string, {
        activeJobs: number;
        closedJobs: number;
        onHoldJobs: number;
    }>();

    // Group jobs by client and count statuses
    jobs.forEach(job => {
        const clientName = job.client.trim();
        if (!clientName) return;

        const clientKey = clientName.toLowerCase();

        if (!clientMap.has(clientKey)) {
            clientMap.set(clientKey, {
                activeJobs: 0,
                closedJobs: 0,
                onHoldJobs: 0,
            });
        }

        const client = clientMap.get(clientKey)!;

        switch (job.job_status) {
            case "Active":
                client.activeJobs++;
                break;
            case "Closed":
                client.closedJobs++;
                break;
            case "On Hold":
            case "Hold":
                client.onHoldJobs++;
                break;
            default:
                // Handle unknown job status
                break;
        }
    });

    // Calculate summary
    let activeClients = 0;
    let holdClients = 0;
    let closedClients = 0;

    clientMap.forEach((data) => {
        if (data.activeJobs > 0) {
            activeClients++;
        } else if (data.onHoldJobs > 0) {
            holdClients++;
        } else {
            closedClients++;
        }
    });

    const totalJobs = jobs.length;
    const activeJobs = jobs.filter(job => job.job_status === 'Active').length;
    const holdJobs = jobs.filter(job => job.job_status === 'Hold' || job.job_status === 'On Hold').length;
    const closedJobs = jobs.filter(job => job.job_status === 'Closed').length;

    return {
        totalClients: clientMap.size,
        activeClients,
        holdClients,
        closedClients,
        totalJobs,
        activeJobs,
        holdJobs,
        closedJobs,
    };
};

/**
 * Get unique client names from jobs
 * Alternative method for backward compatibility
 */
export const getUniqueClientNames = (jobs: Job[]): string[] => {
    const uniqueClients = new Set<string>();
    jobs.forEach(job => {
        if (job.client.trim()) {
            uniqueClients.add(job.client.trim());
        }
    });
    return Array.from(uniqueClients);
};

/**
 * Validate client data consistency
 * Use this to debug mismatches between dashboard and client management
 */
export const validateClientDataConsistency = (jobs: Job[], clientCompanies: any[]) => {
    const jobClients = getUniqueClientNames(jobs);
    const clientCompanyNames = clientCompanies.map((cc: any) => cc.client_name || cc.name || '').filter(Boolean);
    
    const missingInClientCompanies = jobClients.filter(jobClient => 
      !clientCompanyNames.some(ccName => 
        ccName.toLowerCase().includes(jobClient.toLowerCase()) ||
        jobClient.toLowerCase().includes(ccName.toLowerCase())
      )
    );
    
    const missingInJobs = clientCompanyNames.filter(ccName => 
      !jobClients.some(jobClient => 
        ccName.toLowerCase().includes(jobClient.toLowerCase()) ||
        jobClient.toLowerCase().includes(ccName.toLowerCase())
      )
    );
    
    return {
      isConsistent: missingInClientCompanies.length === 0 && missingInJobs.length === 0,
      missingInClientCompanies,
      missingInJobs,
      jobClients,
      clientCompanyNames
    };
  };
