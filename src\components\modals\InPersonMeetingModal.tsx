import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { X, MapPin, User, FileText, Mail, Calendar, Clock, Loader2, CheckCircle, Sparkles } from 'lucide-react';
import { type Candidate } from '@/types/candidate';
import { ApiService, type InPersonMeetingRequest } from '@/services/api';
import { useUser } from '@/contexts/user-context';

interface InPersonMeetingModalProps {
  candidate: Candidate | null;
  isOpen: boolean;
  onClose: () => void;
  selectedDate?: Date;
}

// Skeleton Loader Component
const SkeletonLoader = () => (
  <div className="animate-pulse space-y-4">
    <div className="space-y-3">
      <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-24"></div>
      <div className="h-12 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded-lg"></div>
    </div>
    <div className="space-y-3">
      <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-32"></div>
      <div className="h-12 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded-lg"></div>
    </div>
    <div className="space-y-3">
      <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-28"></div>
      <div className="h-12 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded-lg"></div>
    </div>
    <div className="space-y-3">
      <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-26"></div>
      <div className="h-12 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded-lg"></div>
    </div>
    <div className="grid grid-cols-2 gap-4">
      <div className="space-y-3">
        <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-20"></div>
        <div className="h-12 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded-lg"></div>
      </div>
      <div className="space-y-3">
        <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded w-18"></div>
        <div className="h-12 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 rounded-lg"></div>
      </div>
    </div>
  </div>
);

export function InPersonMeetingModal({ candidate, isOpen, onClose, selectedDate }: InPersonMeetingModalProps) {
  const { userEmail } = useUser();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [formData, setFormData] = useState({
    name: '',
    purpose: '',
    time: '',
    date: '',
    email: '',
    location: '',
  });

  useEffect(() => {
    if (isOpen && candidate) {
      // Use selected date from calendar if provided, otherwise use current date
      let initialDate;
      if (selectedDate) {
        const year = selectedDate.getFullYear();
        const month = String(selectedDate.getMonth() + 1).padStart(2, '0');
        const day = String(selectedDate.getDate()).padStart(2, '0');
        initialDate = `${year}-${month}-${day}`;
      } else {
        initialDate = new Date().toISOString().split('T')[0];
      }

      setFormData({
        name: `${candidate.firstName} ${candidate.lastName}`,
        purpose: `In-Person Meeting | ${candidate.client} | ${candidate.profile}`,
        time: '09:00',
        date: initialDate,
        email: candidate.email || '',
        location: '',
      });
    }
  }, [isOpen, candidate, selectedDate]);

  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };
    let previousOverflow = '';
    if (isOpen) {
      document.addEventListener('keydown', handleEscapeKey);
      previousOverflow = document.body.style.overflow;
      document.body.style.overflow = 'hidden';
    }
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
      document.body.style.overflow = previousOverflow;
    };
  }, [isOpen, onClose]);

  if (!isOpen || !candidate) return null;

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Form validation
  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) errors.name = 'Name is required';
    if (!formData.purpose.trim()) errors.purpose = 'Purpose is required';
    if (!formData.email.trim()) errors.email = 'Email is required';
    else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }
    if (!formData.location.trim()) errors.location = 'Location is required';
    if (!formData.date) {
      errors.date = 'Date is required';
    } else {
      const selectedDate = new Date(formData.date);
      const today = new Date();
      const tomorrow = new Date(today);
      tomorrow.setDate(today.getDate() + 1);

      // Reset time to compare only dates
      selectedDate.setHours(0, 0, 0, 0);
      today.setHours(0, 0, 0, 0);
      tomorrow.setHours(0, 0, 0, 0);

      if (selectedDate < today) {
        errors.date = 'Date cannot be in the past';
      } else if (selectedDate > tomorrow) {
        errors.date = 'Date can only be today or tomorrow';
      }
    }
    if (!formData.time) errors.time = 'Time is required';

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fix the errors and try again.');
      return;
    }

    const userId = localStorage.getItem('userId');
    if (!userId || !userEmail) {
      toast.error('User information not found. Please log in again.');
      return;
    }

    setIsSubmitting(true);
    setFormErrors({});

    try {
      const meetingData: InPersonMeetingRequest = {
        name: formData.name,
        purpose: formData.purpose,
        time: formData.time,
        date: formData.date,
        email: formData.email,
        location: formData.location,
        user_id: userId,
        user_email: userEmail,
      };

      const response = await ApiService.createInPersonEvent(meetingData);

      if (response.status === 'success' || response.status === 'Success') {
        setIsSuccess(true);
        toast.success('In-person meeting scheduled successfully!');

        // Show success animation for 2 seconds before closing
        setTimeout(() => {
          setIsSuccess(false);
          onClose();
        }, 2000);
      } else {
        toast.error(`Failed to schedule in-person meeting: ${response.message || 'Unknown error'}`);
      }
    } catch (error) {
      toast.error('Failed to schedule in-person meeting. Please try again.');
    } finally {
      if (!isSuccess) {
        setIsSubmitting(false);
      }
    }
  };

  return (
    <div
      className={`fixed inset-0 flex items-center justify-center z-50 p-4 transition-all duration-500 ${isSuccess
        ? 'bg-gradient-to-br from-purple-400 via-pink-500 to-indigo-600 backdrop-blur-sm'
        : 'bg-gradient-to-br from-gray-900/80 via-gray-800/70 to-black/60 backdrop-blur-md'
        }`}
      onClick={handleBackdropClick}
    >
      <div className={`bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-hidden transform transition-all duration-500 ${isOpen ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
        } ${isSuccess ? 'ring-4 ring-purple-300 ring-opacity-50' : ''}`}>
        {/* Enhanced Header with Gradient */}
        <div className={`relative overflow-hidden ${isSuccess
          ? 'bg-gradient-to-r from-purple-500 via-pink-600 to-indigo-600'
          : 'bg-gradient-to-r from-purple-500 via-pink-600 to-indigo-600'
          }`}>
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse"></div>
          <div className="relative p-6 text-white">
            <div className="flex items-center justify-center gap-3">
              <div className={`p-3 rounded-full ${isSuccess ? 'bg-white/20' : 'bg-white/10'} backdrop-blur-sm`}>
                {isSuccess ? (
                  <CheckCircle className="h-6 w-6 animate-bounce" />
                ) : (
                  <MapPin className="h-6 w-6" />
                )}
              </div>
              <div className="text-center">
                <h2 className="text-2xl font-bold bg-gradient-to-r from-white to-purple-100 bg-clip-text text-transparent">
                  {isSuccess ? 'Meeting Scheduled!' : 'New In-Person Meeting'}
                </h2>
                {!isSuccess && (
                  <p className="text-purple-100 text-sm mt-1">Schedule a face-to-face meeting</p>
                )}
              </div>
            </div>
          </div>
          {!isSuccess && (
            <button
              onClick={onClose}
              className="absolute top-4 right-4 text-white hover:text-purple-200 transition-all duration-200 p-2 rounded-full hover:bg-white/10"
            >
              <X className="h-5 w-5" />
            </button>
          )}
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)] bg-gradient-to-b from-gray-50 to-white">
          {isSuccess ? (
            <div className="text-center py-12 space-y-6">
              <div className="relative">
                <div className="w-24 h-24 mx-auto bg-gradient-to-r from-purple-400 to-pink-500 rounded-full flex items-center justify-center animate-bounce">
                  <CheckCircle className="h-12 w-12 text-white" />
                </div>
                <div className="absolute -inset-4 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full opacity-20 animate-ping"></div>
              </div>
              <div className="space-y-2">
                <h3 className="text-2xl font-bold text-gray-800">Success!</h3>
                <p className="text-gray-600">Your in-person meeting has been scheduled successfully.</p>
                <div className="flex items-center justify-center gap-2 mt-4">
                  <Sparkles className="h-5 w-5 text-yellow-500 animate-spin" />
                  <span className="text-sm text-gray-500">Redirecting...</span>
                </div>
              </div>
            </div>
          ) : isSubmitting ? (
            <SkeletonLoader />
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6 animate-in fade-in duration-300">
              <div className="space-y-1">
                <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                  <User className="h-4 w-4 mr-2 text-purple-600" />
                  Name *
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className={`w-full pl-10 pr-3 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white ${formErrors.name
                      ? 'border-red-300 focus:ring-red-500 bg-red-50'
                      : 'border-gray-300 focus:ring-purple-500'
                      }`}
                    placeholder="Enter full name"
                    required
                  />
                  <User className={`absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 pointer-events-none transition-colors ${formErrors.name ? 'text-red-400' : 'text-gray-400'
                    }`} />
                </div>
                {formErrors.name && (
                  <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                    <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                    {formErrors.name}
                  </p>
                )}
              </div>
              <div className="space-y-1">
                <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                  <FileText className="h-4 w-4 mr-2 text-purple-600" />
                  Purpose *
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={formData.purpose}
                    onChange={(e) => handleInputChange('purpose', e.target.value)}
                    className={`w-full pl-10 pr-3 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white ${formErrors.purpose
                      ? 'border-red-300 focus:ring-red-500 bg-red-50'
                      : 'border-gray-300 focus:ring-purple-500'
                      }`}
                    placeholder="Enter meeting purpose"
                    required
                  />
                  <FileText className={`absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 pointer-events-none transition-colors ${formErrors.purpose ? 'text-red-400' : 'text-gray-400'
                    }`} />
                </div>
                {formErrors.purpose && (
                  <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                    <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                    {formErrors.purpose}
                  </p>
                )}
              </div>
              <div className="space-y-1">
                <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                  <Mail className="h-4 w-4 mr-2 text-purple-600" />
                  Email *
                </label>
                <div className="relative">
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className={`w-full pl-10 pr-3 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white ${formErrors.email
                      ? 'border-red-300 focus:ring-red-500 bg-red-50'
                      : 'border-gray-300 focus:ring-purple-500'
                      }`}
                    placeholder="Enter email address"
                    required
                  />
                  <Mail className={`absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 pointer-events-none transition-colors ${formErrors.email ? 'text-red-400' : 'text-gray-400'
                    }`} />
                </div>
                {formErrors.email && (
                  <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                    <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                    {formErrors.email}
                  </p>
                )}
              </div>
              <div className="space-y-1">
                <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                  <MapPin className="h-4 w-4 mr-2 text-purple-600" />
                  Location *
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={formData.location}
                    onChange={(e) => handleInputChange('location', e.target.value)}
                    className={`w-full pl-10 pr-3 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white ${formErrors.location
                      ? 'border-red-300 focus:ring-red-500 bg-red-50'
                      : 'border-gray-300 focus:ring-purple-500'
                      }`}
                    placeholder="Enter meeting location"
                    required
                  />
                  <MapPin className={`absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 pointer-events-none transition-colors ${formErrors.location ? 'text-red-400' : 'text-gray-400'
                    }`} />
                </div>
                {formErrors.location && (
                  <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                    <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                    {formErrors.location}
                  </p>
                )}
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                    <Calendar className="h-4 w-4 mr-2 text-purple-600" />
                    Date *
                  </label>
                  <input
                    type="date"
                    value={formData.date}
                    min={new Date().toISOString().split('T')[0]}
                    max={new Date(new Date().setDate(new Date().getDate() + 1)).toISOString().split('T')[0]}
                    onChange={(e) => handleInputChange('date', e.target.value)}
                    className={`w-full px-3 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white ${formErrors.date
                      ? 'border-red-300 focus:ring-red-500 bg-red-50'
                      : 'border-gray-300 focus:ring-purple-500'
                      }`}
                    required
                  />
                  {formErrors.date && (
                    <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                      <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                      {formErrors.date}
                    </p>
                  )}
                </div>
                <div className="space-y-1">
                  <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                    <Clock className="h-4 w-4 mr-2 text-purple-600" />
                    Time *
                  </label>
                  <input
                    type="time"
                    value={formData.time}
                    onChange={(e) => handleInputChange('time', e.target.value)}
                    className={`w-full px-3 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white ${formErrors.time
                      ? 'border-red-300 focus:ring-red-500 bg-red-50'
                      : 'border-gray-300 focus:ring-purple-500'
                      }`}
                    required
                  />
                  {formErrors.time && (
                    <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                      <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                      {formErrors.time}
                    </p>
                  )}
                </div>
              </div>
              <div className="flex justify-end gap-3 pt-6 border-t border-gray-100">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-6 py-3 text-gray-700 bg-gradient-to-r from-gray-100 to-gray-200 rounded-lg hover:from-gray-200 hover:to-gray-300 transition-all duration-200 font-medium shadow-sm hover:shadow-md transform hover:-translate-y-0.5"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-lg hover:from-purple-600 hover:to-pink-700 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 min-w-[180px] justify-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none disabled:hover:shadow-lg"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-5 w-5 animate-spin" />
                      <span className="animate-pulse">Scheduling...</span>
                    </>
                  ) : (
                    <>
                      <MapPin className="h-5 w-5" />
                      <span>Schedule Meeting</span>
                    </>
                  )}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
}


