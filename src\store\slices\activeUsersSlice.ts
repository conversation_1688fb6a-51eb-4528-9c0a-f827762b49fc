import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { ApiService, type ActiveUser, type ActiveUsersResponse } from '@/services/api';
import type { RootState } from '@/store';

// User interface (converted from API format)
export interface User {
  id: number;
  username: string;
  firstName: string;
  lastName: string;
  email: string;
  userType: "management" | "recruiter";
  isVerified: boolean;
  isActive: boolean;
  isPeerReviewer: boolean;
}

// Filter tag interface
export interface FilterTag {
  column: string;
  value: string;
}

// Active Users state interface
export interface ActiveUsersState {
  users: User[];
  loading: boolean;
  error: string | null;
  // Filter and search state
  searchTags: FilterTag[];
  appliedTags: FilterTag[];
  // Pagination state
  currentPage: number;
  itemsPerPage: number;
  // Sorting state
  sortConfig: {
    key: keyof User | null;
    direction: "ascending" | "descending" | null;
  };
}

// Initial state
const initialState: ActiveUsersState = {
  users: [],
  loading: false,
  error: null,
  searchTags: [],
  appliedTags: [],
  currentPage: 1,
  itemsPerPage: 10,
  sortConfig: { key: null, direction: null },
};

// Helper function to convert API user to local format
const convertApiUserToLocal = (apiUser: ActiveUser): User => {
  const nameParts = apiUser.name.split(' ');
  return {
    id: apiUser.id,
    username: apiUser.username,
    firstName: nameParts[0] || '',
    lastName: nameParts.slice(1).join(' ') || '',
    email: apiUser.email,
    userType: apiUser.user_type === 'management' ? 'management' : 'recruiter',
    isVerified: apiUser.is_verified,
    isActive: apiUser.is_active,
    isPeerReviewer: apiUser.peer_reviewer_status,
  };
};

// Async thunk for fetching active users
export const fetchActiveUsers = createAsyncThunk(
  'activeUsers/fetchActiveUsers',
  async (_, { rejectWithValue }) => {
    try {
      const response: ActiveUsersResponse = await ApiService.fetchActiveUsers();
      const allActiveUsers = [
        ...response.active_users_manager,
        ...response.active_users_recruiter,
      ];
      return allActiveUsers.map(convertApiUserToLocal);
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : "An unknown error occurred");
    }
  }
);

// Async thunk for updating user verification status
export const updateUserVerification = createAsyncThunk(
  'activeUsers/updateUserVerification',
  async ({ username, newStatus }: { username: string; newStatus: boolean }) => {
    const currentUserId = localStorage.getItem('userId') || ''; // Keep as string
    const response = await ApiService.disableUser(currentUserId, username, newStatus);
    return { username, newStatus, response };
  }
);

export const updateUserActiveStatus = createAsyncThunk(
  'activeUsers/updateUserActiveStatus',
  async ({ username, newStatus }: { username: string; newStatus: boolean }) => {
    const currentUserId = localStorage.getItem('userId') || ''; // Keep as string
    const currentUserName = localStorage.getItem('userName') || '';
    const response = await ApiService.deactivateUser(currentUserId, username, newStatus, currentUserName);
    return { username, newStatus, response };
  }
);

export const updateUserPeerReviewerStatus = createAsyncThunk(
  'activeUsers/updateUserPeerReviewerStatus',
  async (
    { username, newStatus }: { username: string; newStatus: boolean },
    { getState }
  ) => {
    const state = getState() as RootState;
    const user = state.activeUsers.users.find((u) => u.username === username);
    if (!user) throw new Error('User not found');

    const response = await ApiService.updatePeerReviewerStatus(
      user.id,
      newStatus
    );
    return { username, newStatus, response };
  }
);

// Active Users slice
const activeUsersSlice = createSlice({
  name: 'activeUsers',
  initialState,
  reducers: {
    // Search and filter actions
    addSearchTag: (state, action: PayloadAction<FilterTag>) => {
      const newTag = action.payload;
      if (!state.searchTags.some(t => t.value === newTag.value && t.column === newTag.column)) {
        state.searchTags.push(newTag);
      }
    },
    removeSearchTag: (state, action: PayloadAction<FilterTag>) => {
      const tagToRemove = action.payload;
      state.searchTags = state.searchTags.filter(
        tag => tag.value !== tagToRemove.value || tag.column !== tagToRemove.column
      );
    },
    applyFilters: (state) => {
      state.appliedTags = [...state.searchTags];
      state.currentPage = 1;
    },
    clearAllFilters: (state) => {
      state.searchTags = [];
      state.appliedTags = [];
      state.currentPage = 1;
    },
    clearCache: (state) => {
      state.users = [];
      state.loading = false;
      state.error = null;
      state.searchTags = [];
      state.appliedTags = [];
      state.currentPage = 1;
      state.sortConfig = { key: null, direction: null };
    },
    // Pagination actions
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
    setItemsPerPage: (state, action: PayloadAction<number>) => {
      state.itemsPerPage = action.payload;
      state.currentPage = 1;
    },
    // Sorting actions
    setSortConfig: (state, action: PayloadAction<{
      key: keyof User | null;
      direction: "ascending" | "descending" | null;
    }>) => {
      state.sortConfig = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch active users
      .addCase(fetchActiveUsers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchActiveUsers.fulfilled, (state, action) => {
        state.loading = false;
        state.users = action.payload;
        state.error = null;
      })
      .addCase(fetchActiveUsers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Update user verification
      .addCase(updateUserVerification.fulfilled, (state, action) => {
        const { username, newStatus } = action.payload;
        const userIndex = state.users.findIndex(user => user.username === username);
        if (userIndex !== -1) {
          state.users[userIndex].isVerified = newStatus;
        }
      })
      // Update user active status
      .addCase(updateUserActiveStatus.fulfilled, (state, action) => {
        const { username, newStatus } = action.payload;
        const userIndex = state.users.findIndex(user => user.username === username);
        if (userIndex !== -1) {
          state.users[userIndex].isActive = newStatus;
        }
      })
      // Update user peer reviewer status
      .addCase(updateUserPeerReviewerStatus.fulfilled, (state, action) => {
        const { username, newStatus } = action.payload;
        const userIndex = state.users.findIndex(user => user.username === username);
        if (userIndex !== -1) {
          state.users[userIndex].isPeerReviewer = newStatus;
        }
      });
  },
});

export const {
  addSearchTag,
  removeSearchTag,
  applyFilters,
  clearAllFilters,
  clearCache,
  setCurrentPage,
  setItemsPerPage,
  setSortConfig,
} = activeUsersSlice.actions;

export default activeUsersSlice.reducer;
