import { useEffect, useCallback } from 'react';
import { useAppDispatch } from '@/store/hooks';
import { useUser } from '@/contexts/user-context';
import { fetchJobs } from '@/store/slices/jobsSlice';
import { fetchCandidates } from '@/store/slices/candidatesSlice';
import { fetchActiveUsers } from '@/store/slices/activeUsersSlice';

/**
 * Hook for pre-fetching essential data on login and managing data freshness
 * This ensures users see data immediately on dashboard without waiting
 */
export const useDataPrefetch = () => {
    const dispatch = useAppDispatch();
    const { isAuthenticated, userRole, userName, userId } = useUser();

    // Pre-fetch all essential data when user logs in
    const prefetchAllData = useCallback(async () => {
        if (!isAuthenticated || !userName || !userId) return;

        try {
            // Fetch jobs data (essential for dashboard metrics)
            if (userRole === 'manager') {
                await dispatch(fetchJobs({ username: userName, force: false })).unwrap();
            }

            // Fetch candidates data (essential for dashboard metrics)
            await dispatch(fetchCandidates({
                userId,
                userType: userRole || 'recruiter',
                userName,
                force: false
            })).unwrap();

            // Fetch active users (essential for user management)
            if (userRole === 'manager') {
                await dispatch(fetchActiveUsers()).unwrap();
            }

        } catch (error) {
            // Error pre-fetching data
        }
    }, [dispatch, isAuthenticated, userRole, userName, userId]);

    // Pre-fetch data on login
    useEffect(() => {
        if (isAuthenticated && userName && userId) {
            prefetchAllData();
        }
    }, [isAuthenticated, userName, userId, prefetchAllData]);

    // Refresh data when needed (e.g., after certain actions)
    const refreshData = useCallback(async (force: boolean = false) => {
        if (!isAuthenticated || !userName || !userId) return;

        try {
            // Refreshing data...
            await Promise.all([
                dispatch(fetchJobs({ username: userName, force })),
                dispatch(fetchCandidates({
                    userId,
                    userType: userRole || 'recruiter',
                    userName,
                    force
                })),
                dispatch(fetchActiveUsers())
            ]);

        } catch (error) {
            // Error refreshing data
        }
    }, [dispatch, isAuthenticated, userRole, userName, userId]);

    return {
        prefetchAllData,
        refreshData,
        isAuthenticated,
        userRole,
        userName,
        userId
    };
};
