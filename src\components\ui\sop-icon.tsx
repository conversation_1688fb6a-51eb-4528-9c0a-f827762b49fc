import { BookOpen } from "lucide-react";

interface SOPIconProps {
  size?: "sm" | "md" | "lg";
  variant?: "icon-only" | "with-text";
  className?: string;
}

export function SOPIcon({ size = "md", variant = "icon-only", className = "" }: SOPIconProps) {
  const sizeClasses = {
    sm: "h-8 w-8",
    md: "h-10 w-10",
    lg: "h-12 w-12"
  };

  const iconSizes = {
    sm: "h-4 w-4",
    md: "h-5 w-5",
    lg: "h-6 w-6"
  };

  return (
    <div
      className={`
        inline-flex items-center justify-center gap-2
        bg-blue-50 hover:bg-blue-100 
        text-blue-600 hover:text-blue-700
        border border-blue-200 hover:border-blue-300
        rounded-full transition-all duration-200
        ${sizeClasses[size]}
        ${className}
      `}
      title="Recruitment & Selection SOP"
    >
      <BookOpen className={iconSizes[size]} />
      {variant === "with-text" && (
        <span className="text-sm font-medium">SOP</span>
      )}
    </div>
  );
}
