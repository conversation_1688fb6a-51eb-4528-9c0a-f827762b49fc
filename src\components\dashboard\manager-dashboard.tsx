import { useNavigate } from "react-router-dom";
import { Users, Building2, User<PERSON>heck, TrendingUp, Calendar, Target, Settings } from "lucide-react";
import { useDataManagement } from "@/hooks/use-data-management";
import { useAppSelector } from "@/store/hooks";
import { selectClientCompanies } from "@/store/selectors/jobsSelectors";
import { validateClientDataConsistency } from "@/utils/client-utils";
import React, { useState } from "react";
import { TargetManagementModal, RecruiterTargets } from "@/components/modals/target-management-modal";

export function ManagerDashboard() {
  const navigate = useNavigate();

  // Target management state
  const [isTargetModalOpen, setIsTargetModalOpen] = useState(false);
  const [selectedRecruiter, setSelectedRecruiter] = useState<{ id: string; name: string } | null>(null);
  const [recruiterTargets, setRecruiterTargets] = useState<RecruiterTargets[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [timePeriodFilter, setTimePeriodFilter] = useState<'weekly' | 'monthly'>('monthly');

  // Use optimized data management hook
  const {
    jobs,
    candidates,
    activeUsers,
    jobsLoading,
    candidatesLoading,
    activeUsersLoading,
  } = useDataManagement();

  // Get client companies data for consistent calculation
  const clientCompanies = useAppSelector(selectClientCompanies);

  // Calculate real metrics from optimized data
  const dashboardData = {
    activeClients: clientCompanies.filter(client => client.status === 'Active').length, // Only count active clients
    totalRecruiters: candidates.length > 0 ? new Set(candidates.map(c => c.recruiter || c.management).filter(Boolean)).size : 0, // Count unique recruiters from candidate data
    activeJobs: jobs.filter(job => job.job_status === 'Active').length, // Only count active jobs
    totalActivePositions: jobs
      .filter(job => job.job_status === 'Active')
      .reduce((sum, job) => sum + (Number(job.no_of_positions) || 1), 0), // Sum of positions for active jobs
    candidatesThisMonth: candidates.length > 0 ? candidates.filter(candidate => {
      const candidateDate = new Date(candidate.appliedDate);
      const monthStart = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
      return candidateDate >= monthStart;
    }).length : 0,
    clientInterviewsInProgress: candidates.filter(candidate =>
      candidate.status?.toLowerCase().includes('client interview') &&
      candidate.status?.toLowerCase().includes('progress')
    ).length, // Real data: Count candidates in client interview stage
  };

  // Validate client data consistency
  React.useEffect(() => {
    const validation = validateClientDataConsistency(jobs, clientCompanies);
    if (!validation.isConsistent) {
      // Client data mismatch detected
    }
  }, [jobs, clientCompanies]);


  // Target management functions
  const handleOpenTargetModal = (recruiter: { id: string; name: string }) => {
    setSelectedRecruiter(recruiter);
    setIsTargetModalOpen(true);
  };

  const handleCloseTargetModal = () => {
    setIsTargetModalOpen(false);
    setSelectedRecruiter(null);
  };

  const handleSaveTargets = (targets: RecruiterTargets) => {
    setRecruiterTargets(prev => {
      const filtered = prev.filter(t =>
        !(t.recruiterId === targets.recruiterId && t.timePeriod === targets.timePeriod)
      );
      return [...filtered, targets];
    });

    // Save to localStorage with recruiter-specific key
    const existingTargets = JSON.parse(localStorage.getItem('recruiterTargets') || '[]');
    const filtered = existingTargets.filter((t: RecruiterTargets) =>
      !(t.recruiterId === targets.recruiterId && t.timePeriod === targets.timePeriod)
    );
    const updatedTargets = [...filtered, targets];
    localStorage.setItem('recruiterTargets', JSON.stringify(updatedTargets));

    // Also save to recruiter-specific key for easy access
    localStorage.setItem(`recruiterTargets_${targets.recruiterId}`, JSON.stringify(targets));

    setSelectedRecruiter(null);
    setIsTargetModalOpen(false);
  };

  // Load targets from localStorage on component mount
  React.useEffect(() => {
    // Load recruiter targets from localStorage
    const savedTargets = localStorage.getItem('recruiterTargets');
    if (savedTargets) {
      try {
        const parsedTargets = JSON.parse(savedTargets);
        setRecruiterTargets(parsedTargets);
      } catch (error) {
        // Error loading recruiter targets
      }
    }
  }, []);

  // Save targets to localStorage whenever they change
  React.useEffect(() => {
    localStorage.setItem('recruiterTargets', JSON.stringify(recruiterTargets));
  }, [recruiterTargets]);

  const StatCard = ({ title, value, icon: Icon, trend, color, onClick, loading }: {
    title: string;
    value: number | string;
    icon: any;
    trend?: string;
    color: string;
    onClick?: () => void;
    loading?: boolean;
  }) => (
    <div
      className={`bg-white rounded-lg shadow-md p-6 border border-gray-200 hover:shadow-lg transition-shadow ${onClick ? 'cursor-pointer hover:scale-105' : ''}`}
      onClick={onClick}
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          {loading ? (
            <div className="animate-pulse bg-gray-200 h-8 w-16 rounded mt-1"></div>
          ) : (
            <p className="text-3xl font-bold text-gray-900">{value}</p>
          )}
          {trend && (
            <p className="text-sm text-green-600 mt-1">{trend}</p>
          )}
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex-1 p-6 bg-gray-50 overflow-y-auto h-full">
      <div className="max-w-7xl mx-auto">
        {/* Header with Refresh Button */}
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Manager Dashboard</h1>
            <p className="text-gray-600 mt-2">Welcome back! Here's what's happening with your recruitment today.</p>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <StatCard
            title="Active Clients"
            value={dashboardData.activeClients}
            icon={Building2}
            color="bg-blue-500"
            onClick={() => navigate("/manager/clients")}
            loading={jobsLoading}
          />
          <StatCard
            title="Total Recruiters"
            value={dashboardData.totalRecruiters}
            icon={Users}
            color="bg-green-500"
            onClick={() => navigate("/manager/recruiters")}
            loading={activeUsersLoading}
          />
          <StatCard
            title="Active Jobs"
            value={dashboardData.activeJobs}
            icon={Target}
            color="bg-purple-500"
            onClick={() => navigate("/manager/jobs-overview")}
            loading={jobsLoading}
          />

          <StatCard
            title="Total Active Positions"
            value={dashboardData.totalActivePositions}
            icon={Target}
            color="bg-indigo-500"
            onClick={() => navigate("/manager/jobs-overview")}
            loading={jobsLoading}
          />

          <StatCard
            title="Client Interviews In Progress"
            value={dashboardData.clientInterviewsInProgress}
            icon={Calendar}
            color="bg-teal-500"
          />
          <StatCard
            title="Candidates This Month"
            value={dashboardData.candidatesThisMonth}
            icon={UserCheck}
            color="bg-orange-500"
            loading={candidatesLoading}
          />
        </div>

        {/* Recruitment Incentive Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {/* One-Time Hiring Incentive */}
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg shadow-md p-6 border border-green-200">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-green-800">One-Time Hiring Incentive</h3>
                <p className="text-sm text-green-600 mt-1">Contract & Project-based Placements</p>
              </div>
              <div className="p-3 rounded-full bg-green-500">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
            </div>

            <div className="space-y-3">
              <div className="bg-white rounded-lg p-4 border border-green-100">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-700">Incentive Amount</span>
                  <span className="text-2xl font-bold text-green-600">₹15,000</span>
                </div>
                {/* <p className="text-xs text-gray-500 mt-1">Per successful placement</p> */}
              </div>
            </div>
          </div>

          {/* Full-Time Hiring Incentive */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg shadow-md p-6 border border-blue-200">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-blue-800">Full-Time Hiring Incentive</h3>
                <p className="text-sm text-blue-600 mt-1">Permanent & Long-term Placements</p>
              </div>
              <div className="p-3 rounded-full bg-blue-500">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>

            <div className="space-y-3">
              <div className="bg-white rounded-lg p-4 border border-blue-100">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-700">Incentive Amount</span>
                  <span className="text-2xl font-bold text-blue-600">₹25,000</span>
                </div>
                {/* <p className="text-xs text-gray-500 mt-1">Per successful placement</p> */}
              </div>
            </div>
          </div>
        </div>

        {/* Important Notes */}
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
          <div className="flex items-start space-x-3">
            <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-white text-sm font-bold">!</span>
            </div>
            <div>
              <h4 className="text-sm font-semibold text-red-800 mb-2">Important Notes</h4>
              <div className="space-y-2 text-sm text-red-700">
                <div className="flex items-start space-x-2">
                  <span className="text-red-500 font-bold">•</span>
                  <span><strong>Incentive is NOT applicable</strong> for hiring Freshers / Trainees</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-red-500 font-bold">•</span>
                  <span><strong>Incentive is NOT applicable</strong> for non-billable hiring</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-red-500 font-bold">•</span>
                  <span>All placements must be verified and approved by management</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-red-500 font-bold">•</span>
                  <span>Incentive is paid after successful completion of 3 Month period</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recruiter Target Management */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6 mb-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Settings className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Recruiter Target Management</h2>
                <p className="text-sm text-gray-600">Set and manage performance targets for recruiters</p>
              </div>
            </div>
          </div>

          {/* Time Period Filter */}
          <div className="mb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <span className="text-sm font-medium text-gray-700">Time Period:</span>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setTimePeriodFilter('weekly')}
                    className={`px-3 py-1 text-sm rounded-md transition-colors ${timePeriodFilter === 'weekly'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                  >
                    Weekly
                  </button>
                  <button
                    onClick={() => setTimePeriodFilter('monthly')}
                    className={`px-3 py-1 text-sm rounded-md transition-colors ${timePeriodFilter === 'monthly'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                  >
                    Monthly
                  </button>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-500">
                  {activeUsers.filter(user => user.userType === 'recruiter').length} total recruiters • {recruiterTargets.filter(t => t.timePeriod === timePeriodFilter).length} with {timePeriodFilter} targets
                </span>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search recruiters..."
                    className="pl-8 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  <Users className="h-4 w-4 absolute left-2.5 top-2.5 text-gray-400" />
                </div>
              </div>
            </div>
          </div>

          {/* Recruiters Grid with Fixed Height and Scrollbar */}
          <div className="h-96 overflow-y-auto pr-2 scrollbar-thin scrollbar-track-gray-100 scrollbar-thumb-gray-300 hover:scrollbar-thumb-gray-400">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {activeUsers
                .filter(user => user.userType === 'recruiter')
                .filter(user =>
                  user.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                  user.lastName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                  user.email.toLowerCase().includes(searchTerm.toLowerCase())
                )
                .map(recruiter => {
                  const existingTargets = recruiterTargets.find(t => t.recruiterId === recruiter.id.toString() && t.timePeriod === timePeriodFilter);
                  const hasTargets = !!existingTargets;

                  return (
                    <div
                      key={recruiter.id}
                      className={`border rounded-lg p-4 transition-all cursor-pointer hover:shadow-md ${hasTargets
                        ? 'border-green-200 bg-green-50'
                        : 'border-gray-200 bg-gray-50'
                        }`}
                      onClick={() => handleOpenTargetModal({
                        id: recruiter.id.toString(),
                        name: `${recruiter.firstName} ${recruiter.lastName}`.trim() || recruiter.username
                      })}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium text-gray-900 truncate">{recruiter.firstName} {recruiter.lastName}</h3>
                        <div className="flex items-center space-x-2">
                          {hasTargets && (
                            <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                              {existingTargets?.timePeriod === 'weekly' ? 'Weekly' : 'Monthly'} Targets
                            </span>
                          )}
                          <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                            {recruiter.userType}
                          </span>
                        </div>
                      </div>

                      {hasTargets && (
                        <div className="space-y-1 text-xs text-gray-600">
                          <div className="flex justify-between">
                            <span>Profiles:</span>
                            <span className="font-medium">{existingTargets.targets.profilesSubmitted.min}-{existingTargets.targets.profilesSubmitted.max}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Interviews:</span>
                            <span className="font-medium">{existingTargets.targets.interviewsScheduled.min}-{existingTargets.targets.interviewsScheduled.max}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Offers:</span>
                            <span className="font-medium">{existingTargets.targets.offers.min}-{existingTargets.targets.offers.max}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Joiners:</span>
                            <span className="font-medium">{existingTargets.targets.joiners.min}-{existingTargets.targets.joiners.max}</span>
                          </div>
                          <div className="text-xs text-gray-500 mt-2 text-center">
                            Updated: {new Date(existingTargets.lastUpdated).toLocaleDateString()}
                          </div>
                        </div>
                      )}

                      {!hasTargets && (
                        <div className="text-center py-3">
                          <p className="text-xs text-gray-500">No targets set</p>
                          <p className="text-xs text-gray-400 mt-1">Click to set targets</p>
                        </div>
                      )}
                    </div>
                  );
                })}
            </div>

            {activeUsers.filter(user => user.userType === 'recruiter').length === 0 && (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                <h3 className="text-sm font-medium text-gray-900 mb-1">No Recruiters Found</h3>
                <p className="text-sm text-gray-500">Add recruiters to set performance targets</p>
              </div>
            )}

            {searchTerm && activeUsers.filter(user => user.userType === 'recruiter').filter(user => {
              const fullName = `${user.firstName} ${user.lastName}`.trim() || user.username;
              return fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                user.email.toLowerCase().includes(searchTerm.toLowerCase());
            }).length === 0 && (
                <div className="text-center py-6">
                  <p className="text-sm text-gray-500">No recruiters found matching "{searchTerm}"</p>
                  <button
                    onClick={() => setSearchTerm('')}
                    className="text-blue-600 hover:text-blue-700 text-sm mt-2"
                  >
                    Clear search
                  </button>
                </div>
              )}
          </div>
        </div>



        {/* Data Status Indicators */}
        {/* <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-600">Active Jobs Data</span>
              <div className="flex items-center gap-2">
                {jobsLoading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                ) : dashboardData.activeJobs > 0 ? (
                  <span className="text-green-600 text-sm">✓ {dashboardData.activeJobs} active jobs</span>
                ) : (
                  <span className="text-red-600 text-sm">No active jobs</span>
                )}
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-600">Active Clients Data</span>
              <div className="flex items-center gap-2">
                {jobsLoading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                ) : dashboardData.activeClients > 0 ? (
                  <span className="text-green-600 text-sm">✓ {dashboardData.activeClients} active clients</span>
                ) : (
                  <span className="text-red-600 text-sm">No active clients</span>
                )}
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-600">Users Data</span>
              <div className="flex items-center gap-2">
                {activeUsersLoading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                ) : activeUsers.length > 0 ? (
                  <span className="text-green-600 text-sm">✓ {activeUsers.length} users</span>
                ) : (
                  <span className="text-red-600 text-sm">No data</span>
                )}
              </div>
            </div>
          </div>
        </div> */}

      </div>

      {/* Target Management Modal */}
      <TargetManagementModal
        isOpen={isTargetModalOpen}
        onClose={handleCloseTargetModal}
        recruiter={selectedRecruiter}
        onSave={handleSaveTargets}
        existingTargets={recruiterTargets.find(t => t.recruiterId === selectedRecruiter?.id)}
      />
    </div>
  );
}
