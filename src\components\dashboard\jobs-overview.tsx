import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAppSelector } from "@/store/hooks";
import { selectJobs, selectJobsLoading } from "@/store/selectors/jobsSelectors";
import {
    ArrowLeft,
    Search,
    Target,
    AlertTriangle,
    CheckCircle,
    XCircle
} from "lucide-react";
import { AnimatedTableWrapper } from "@/components/ui/animated-table-wrapper";
import { TableSkeleton } from "@/components/ui/table-skeleton";
import { AnimatedPagination } from "@/components/ui/animated-pagination";
import { useTableAnimation } from "@/hooks/use-table-animation";

export function JobsOverview() {
    const navigate = useNavigate();
    const jobs = useAppSelector(selectJobs);
    const jobsLoading = useAppSelector(selectJobsLoading);

    // Local state
    const [searchTerm, setSearchTerm] = useState("");
    const [statusFilter, setStatusFilter] = useState<string>("Active"); // Default to Active jobs
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(10);

    // Table animation
    const { animatePagination } = useTableAnimation();

    // Calculate job statistics
    const jobStats = {
        totalActive: jobs.filter(job => job.job_status === 'Active').length,
        totalHold: jobs.filter(job => job.job_status === 'Hold' || job.job_status === 'On Hold').length,
        totalClosed: jobs.filter(job => job.job_status === 'Closed').length,
        totalPositions: jobs
            .filter(job => job.job_status === 'Active')
            .reduce((sum, job) => sum + (Number(job.no_of_positions) || 1), 0)
    };



    // Handle card clicks for filtering
    const handleCardClick = (status: string) => {
        if (statusFilter === status) {
            setStatusFilter("all"); // Toggle off if already selected
        } else {
            setStatusFilter(status);
        }
        setCurrentPage(1); // Reset to first page
    };

    // Update filtered jobs to handle "On Hold" status
    const getFilteredJobs = () => {
        let filtered = jobs.filter(job => {
            const matchesSearch =
                job.role?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                job.client?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                job.recruiter?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                job.management?.toLowerCase().includes(searchTerm.toLowerCase());

            let matchesStatus = true;
            if (statusFilter !== "all") {
                if (statusFilter === "Hold") {
                    matchesStatus = job.job_status === "Hold" || job.job_status === "On Hold";
                } else {
                    matchesStatus = job.job_status === statusFilter;
                }
            }

            return matchesSearch && matchesStatus;
        });

        return filtered;
    };

    const filteredJobs = getFilteredJobs();

    // Pagination
    const totalPages = Math.ceil(filteredJobs.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const paginatedJobs = filteredJobs.slice(startIndex, startIndex + itemsPerPage);

    // Handle page change
    const handlePageChange = async (page: number) => {
        await animatePagination();
        setCurrentPage(page);
    };

    // Get status styling
    const getStatusStyling = (status: string) => {
        switch (status) {
            case 'Active':
                return {
                    badge: 'bg-green-100 text-green-800',
                    icon: CheckCircle,
                    iconColor: 'text-green-600'
                };
            case 'Hold':
            case 'On Hold':
                return {
                    badge: 'bg-yellow-100 text-yellow-800',
                    icon: AlertTriangle,
                    iconColor: 'text-yellow-600'
                };
            case 'Closed':
                return {
                    badge: 'bg-red-100 text-red-800',
                    icon: XCircle,
                    iconColor: 'text-red-600'
                };
            default:
                return {
                    badge: 'bg-gray-100 text-gray-800',
                    icon: Target,
                    iconColor: 'text-gray-600'
                };
        }
    };

    // Stat Card Component
    const StatCard = ({ title, value, icon: Icon, color, trend, onClick, isSelected }: {
        title: string;
        value: number;
        icon: any;
        color: string;
        trend?: string;
        onClick?: () => void;
        isSelected?: boolean;
    }) => (
        <div
            className={`bg-white rounded-lg shadow-md p-6 border-2 transition-all ${onClick ? 'cursor-pointer hover:shadow-lg' : ''} ${isSelected ? 'border-blue-500 shadow-lg' : 'border-gray-200'
                }`}
            onClick={onClick}
        >
            <div className="flex items-center justify-between">
                <div>
                    <p className="text-sm font-medium text-gray-600">{title}</p>
                    <p className="text-3xl font-bold text-gray-900">{value}</p>
                    {trend && (
                        <p className="text-sm text-green-600 mt-1">{trend}</p>
                    )}
                </div>
                <div className={`p-3 rounded-full ${color}`}>
                    <Icon className="h-6 w-6 text-white" />
                </div>
            </div>
        </div>
    );

    return (
        <div className="flex-1 p-6 bg-gray-50 overflow-y-auto h-full">
            <div className="max-w-7xl mx-auto">
                {/* Header */}
                <div className="mb-8 flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <button
                            onClick={() => navigate("/manager/dashboard")}
                            className="p-2 rounded-lg bg-white border border-gray-200 hover:bg-gray-50 transition-colors"
                        >
                            <ArrowLeft className="h-5 w-5 text-gray-600" />
                        </button>
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900">Jobs Overview</h1>
                            <p className="text-gray-600 mt-2">Active jobs and their total positions</p>
                        </div>
                    </div>
                </div>

                {/* Statistics Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <StatCard
                        title="Active Jobs"
                        value={jobStats.totalActive}
                        icon={CheckCircle}
                        color="bg-green-500"
                        trend="Currently Active"
                    />
                    <StatCard
                        title="Total Positions"
                        value={jobStats.totalPositions}
                        icon={Target}
                        color="bg-purple-500"
                        trend="Active Jobs Only"
                    />
                </div>

                {/* Search and Items Per Page */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                    <div className="flex flex-col md:flex-row gap-4">
                        {/* Search */}
                        <div className="flex-1">
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                                <input
                                    type="text"
                                    placeholder="Search jobs by role, client, recruiter, or management..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>
                        </div>

                        {/* Items per page */}
                        <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">Show:</span>
                            <select
                                value={itemsPerPage}
                                onChange={(e) => {
                                    setItemsPerPage(Number(e.target.value));
                                    setCurrentPage(1);
                                }}
                                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            >
                                <option value={10}>10</option>
                                <option value={25}>25</option>
                                <option value={50}>50</option>
                                <option value={100}>100</option>
                            </select>
                        </div>
                    </div>
                </div>

                {/* Jobs Table */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div className="px-4 py-2 border-b border-gray-200">
                        <div className="flex items-center justify-between">
                            <h3 className="text-lg font-medium text-gray-900">
                                Active Jobs ({filteredJobs.length} found)
                            </h3>
                            {statusFilter !== "all" && (
                                <button
                                    onClick={() => setStatusFilter("all")}
                                    className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                                >
                                    Clear Filter
                                </button>
                            )}
                        </div>
                    </div>

                    {jobsLoading ? (
                        <TableSkeleton />
                    ) : (
                        <AnimatedTableWrapper>
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Job ID
                                            </th>
                                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Job Title
                                            </th>
                                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Client
                                            </th>

                                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider max-w-xs">
                                                Recruiter
                                            </th>
                                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Management
                                            </th>
                                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Posted Date
                                            </th>
                                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Positions
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {paginatedJobs.length > 0 ? (
                                            paginatedJobs.map((job) => {
                                                const statusStyling = getStatusStyling(job.job_status);
                                                const StatusIcon = statusStyling.icon;

                                                return (
                                                    <tr
                                                        key={job.id}
                                                        className="hover:bg-gray-50 cursor-pointer"
                                                        onClick={() => navigate(`/manager/jobs-overview/${job.id}`)}
                                                    >
                                                        <td className="px-4 py-2 whitespace-nowrap">
                                                            <div className="text-sm font-medium text-gray-900">
                                                                #{job.id}
                                                            </div>
                                                        </td>
                                                        <td className="px-4 py-2 whitespace-nowrap">
                                                            <div className="text-sm font-medium text-gray-900">
                                                                {job.role || 'N/A'}
                                                            </div>
                                                        </td>
                                                        <td className="px-4 py-2 whitespace-nowrap">
                                                            <div className="text-sm text-gray-900">
                                                                {job.client || 'N/A'}
                                                            </div>
                                                        </td>

                                                        <td className="px-4 py-2 whitespace-nowrap max-w-xs">
                                                            <div className="text-sm text-gray-900 truncate" title={job.recruiter || 'N/A'}>
                                                                {job.recruiter || 'N/A'}
                                                            </div>
                                                        </td>
                                                        <td className="px-4 py-2 whitespace-nowrap">
                                                            <div className="text-sm text-gray-900">
                                                                {job.management || 'N/A'}
                                                            </div>
                                                        </td>
                                                        <td className="px-4 py-2 whitespace-nowrap">
                                                            <div className="text-sm text-gray-900">
                                                                {job.date_created ? new Date(job.date_created).toLocaleDateString() : 'N/A'}
                                                            </div>
                                                        </td>
                                                        <td className="px-4 py-2 whitespace-nowrap">
                                                            <div className="text-sm text-gray-900">
                                                                {job.no_of_positions || 1}
                                                            </div>
                                                        </td>
                                                    </tr>
                                                );
                                            })
                                        ) : (
                                            <tr>
                                                <td colSpan={7} className="px-4 py-2 text-center text-sm text-gray-500">
                                                    No jobs found matching your criteria.
                                                </td>
                                            </tr>
                                        )}
                                    </tbody>
                                </table>
                            </div>
                        </AnimatedTableWrapper>
                    )}

                    {/* Pagination */}
                    {totalPages > 1 && (
                        <div className="px-4 py-2 border-t border-gray-200">
                            <div className="flex items-center justify-between">
                                <div className="text-sm text-gray-700">
                                    Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredJobs.length)} of {filteredJobs.length} jobs
                                </div>
                                <AnimatedPagination
                                    currentPage={currentPage}
                                    totalPages={totalPages}
                                    onPageChange={handlePageChange}
                                />
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}
