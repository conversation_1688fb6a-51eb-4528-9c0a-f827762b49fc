import { store } from './index';
import { clearCache as clearCandidatesCache } from './slices/candidatesSlice';
import { clearJobsCache } from './slices/jobsSlice';
import { clearCache as clearActiveUsersCache } from './slices/activeUsersSlice';

/**
 * Clear all Redux stores - used during logout to prevent data leakage between users
 */
export const clearAllStores = () => {
    store.dispatch(clearCandidatesCache());
    store.dispatch(clearJobsCache());
    store.dispatch(clearActiveUsersCache());
};
