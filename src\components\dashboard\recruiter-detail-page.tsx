import { useState, useMemo } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Users, ArrowLeft, Search, FileText, CheckCircle, XCircle, Clock } from "lucide-react";
import { useAppSelector } from "@/store/hooks";
import { selectCandidates, selectCandidatesLoading } from "@/store/selectors/candidatesSelectors";
import { AnimatedPagination } from "@/components/ui/animated-pagination";

export function RecruiterDetailPage() {
  const navigate = useNavigate();
  const { recruiterId } = useParams();
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const itemsPerPage = 10;

  // Get candidates data from Redux store
  const candidates = useAppSelector(selectCandidates);
  const candidatesLoading = useAppSelector(selectCandidatesLoading);

  // Get real recruiter data and candidates for this specific recruiter
  const recruiterData = useMemo(() => {
    if (!candidates || candidates.length === 0) return null;

    // Find the recruiter name from the URL parameter
    const recruiterName = decodeURIComponent(recruiterId || '');

    // Get all candidates for this recruiter
    const recruiterCandidates = candidates.filter(candidate => {
      const candidateRecruiter = candidate.recruiter || candidate.management;
      return candidateRecruiter === recruiterName;
    });

    if (recruiterCandidates.length === 0) return null;

    // Calculate performance metrics
    const totalSubmissions = recruiterCandidates.length;
    const selected = recruiterCandidates.filter(candidate => {
      return candidate.status === "Onboarded" || candidate.status === "Hired";
    }).length;
    const rejected = recruiterCandidates.filter(candidate => {
      if (!candidate.status) return false;
      const status = candidate.status.toLowerCase();
      return status.includes('rejected') || status.includes('declined') || status.includes('dropped');
    }).length;
    const successRate = totalSubmissions > 0 ? Math.round((selected / totalSubmissions) * 100) : 0;

    return {
      name: recruiterName,
      totalSubmissions,
      selected,
      rejected,
      successRate,
      candidates: recruiterCandidates
    };
  }, [candidates, recruiterId]);

  // Filter and pagination logic
  const filteredCandidates = useMemo(() => {
    if (!recruiterData) return [];

    let filtered = recruiterData.candidates;

    if (searchTerm) {
      filtered = filtered.filter(candidate =>
        `${candidate.firstName} ${candidate.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
        candidate.profile.toLowerCase().includes(searchTerm.toLowerCase()) ||
        candidate.client.toLowerCase().includes(searchTerm.toLowerCase()) ||
        candidate.status.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter) {
      filtered = filtered.filter(candidate => {
        if (statusFilter === 'selected') {
          return candidate.status === "Onboarded" || candidate.status === "Hired";
        } else if (statusFilter === 'rejected') {
          if (!candidate.status) return false;
          const status = candidate.status.toLowerCase();
          return status.includes('rejected') || status.includes('declined') || status.includes('dropped');
        } else if (statusFilter === 'inProgress') {
          // Show candidates that are not selected or rejected
          if (candidate.status === "Onboarded" || candidate.status === "Hired") {
            return false;
          }
          if (candidate.status) {
            const status = candidate.status.toLowerCase();
            if (status.includes('rejected') || status.includes('declined') || status.includes('dropped')) {
              return false;
            }
          }
          return true;
        }
        // For 'submissions' (null filter), show all candidates
        return true;
      });
    }

    return filtered;
  }, [recruiterData, searchTerm, statusFilter]);

  const totalPages = Math.ceil(filteredCandidates.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedCandidates = filteredCandidates.slice(startIndex, startIndex + itemsPerPage);

  // Reset to first page when search changes
  useMemo(() => {
    setCurrentPage(1);
  }, [searchTerm, statusFilter]);

  const handleBackToRecruiters = () => {
    const userRole = localStorage.getItem('userRole') || 'manager';
    navigate(`/${userRole}/recruiters`);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'shortlisted':
        return 'bg-green-100 text-green-800';
      case 'interview scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'offer extended':
        return 'bg-purple-100 text-purple-800';
      case 'in progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="flex-1 p-6 bg-gray-50 overflow-y-auto h-full">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={handleBackToRecruiters}
              className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Recruiters
            </button>

            <div className="relative">
              <Search className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search candidates..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {candidatesLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading recruiter data...</p>
            </div>
          ) : !recruiterData ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Recruiter Not Found</h2>
              <p className="text-gray-600 mb-4">No candidates found for this recruiter or the recruiter doesn't exist.</p>
              <button
                onClick={handleBackToRecruiters}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Back to Recruiters
              </button>
            </div>
          ) : (
            <>
              <div className="flex items-center space-x-4 mb-4">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                  <Users className="h-8 w-8 text-green-600" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">{recruiterData.name}</h1>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div
                  className={`bg-white rounded-lg shadow-md p-6 border cursor-pointer transition-all hover:shadow-lg ${statusFilter === null ? 'border-blue-200 bg-blue-50' : 'border-gray-200'
                    }`}
                  onClick={() => setStatusFilter(null)}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Submissions</p>
                      <p className="text-3xl font-bold text-blue-600">{recruiterData?.totalSubmissions || 0}</p>
                    </div>
                    <div className="p-3 rounded-full bg-blue-500">
                      <FileText className="h-6 w-6 text-white" />
                    </div>
                  </div>
                </div>
                <div
                  className={`bg-white rounded-lg shadow-md p-6 border cursor-pointer transition-all hover:shadow-lg ${statusFilter === 'selected' ? 'border-green-200 bg-green-50' : 'border-gray-200'
                    }`}
                  onClick={() => setStatusFilter('selected')}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Selected</p>
                      <p className="text-3xl font-bold text-green-600">{recruiterData?.selected || 0}</p>
                    </div>
                    <div className="p-3 rounded-full bg-green-500">
                      <CheckCircle className="h-6 w-6 text-white" />
                    </div>
                  </div>
                </div>
                <div
                  className={`bg-white rounded-lg shadow-md p-6 border cursor-pointer transition-all hover:shadow-lg ${statusFilter === 'rejected' ? 'border-red-200 bg-red-50' : 'border-gray-200'
                    }`}
                  onClick={() => setStatusFilter('rejected')}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Rejected</p>
                      <p className="text-3xl font-bold text-red-600">{recruiterData?.rejected || 0}</p>
                    </div>
                    <div className="p-3 rounded-full bg-red-500">
                      <XCircle className="h-6 w-6 text-white" />
                    </div>
                  </div>
                </div>
                <div
                  className={`bg-white rounded-lg shadow-md p-6 border cursor-pointer transition-all hover:shadow-lg ${statusFilter === 'inProgress' ? 'border-yellow-200 bg-yellow-50' : 'border-gray-200'
                    }`}
                  onClick={() => setStatusFilter('inProgress')}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">In Progress</p>
                      <p className="text-3xl font-bold text-yellow-600">{((recruiterData?.totalSubmissions || 0) - (recruiterData?.selected || 0) - (recruiterData?.rejected || 0))}</p>
                    </div>
                    <div className="p-3 rounded-full bg-yellow-500">
                      <Clock className="h-6 w-6 text-white" />
                    </div>
                  </div>
                </div>
              </div>


              {/* Filter Status Display */}
              {statusFilter && (
                <div className="mb-4 flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">Filtered by:</span>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${statusFilter === 'selected' ? 'bg-green-100 text-green-800' :
                      statusFilter === 'rejected' ? 'bg-red-100 text-red-800' :
                        statusFilter === 'inProgress' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-blue-100 text-blue-800'
                      }`}>
                      {statusFilter === 'selected' ? 'Selected Candidates' :
                        statusFilter === 'rejected' ? 'Rejected Candidates' :
                          statusFilter === 'inProgress' ? 'In Progress' :
                            statusFilter}
                    </span>
                  </div>
                  <button
                    onClick={() => setStatusFilter(null)}
                    className="text-sm text-blue-600 hover:text-blue-700 underline"
                  >
                    Clear Filter
                  </button>
                </div>
              )}
              {/* </div> */}

              {/* Candidates Table */}
              <div className="bg-white rounded-lg shadow-md border border-gray-200">

                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Applied Date</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Job ID</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Profile</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Skills</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Peer Reviewer</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {candidatesLoading ? (
                        <tr>
                          <td colSpan={12} className="px-6 py-8 text-center">
                            <div className="flex items-center justify-center">
                              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                              <span className="ml-2 text-gray-500">Loading candidates...</span>
                            </div>
                          </td>
                        </tr>
                      ) : filteredCandidates.length === 0 ? (
                        <tr>
                          <td colSpan={12} className="px-6 py-8 text-center text-gray-500">
                            {searchTerm || statusFilter ? 'No candidates found matching the current filters' : 'No candidates found for this recruiter'}
                          </td>
                        </tr>
                      ) : (
                        paginatedCandidates.map((candidate) => (
                          <tr key={candidate.id} className="hover:bg-gray-50">
                            <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-900">{candidate.appliedDate}</td>
                            <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-900">{candidate.lastUpdated}</td>
                            <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-900 font-mono">{candidate.jobId}</td>
                            <td className="px-6 py-2 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                  <span className="text-green-600 font-medium text-xs">
                                    {`${candidate.firstName} ${candidate.lastName}`.split(' ').map((word: string) => word[0]).join('')}
                                  </span>
                                </div>
                                <div className="text-sm font-medium text-gray-900">{`${candidate.firstName} ${candidate.lastName}`}</div>
                              </div>
                            </td>
                            <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-900">
                              <div>
                                <div className="font-medium">{candidate.experience} years</div>
                                <div className="text-gray-500">{candidate.location}</div>
                              </div>
                            </td>
                            <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-900">{candidate.email}</td>
                            <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-900">{candidate.phone}</td>
                            <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-900">{candidate.client}</td>
                            <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-900">{candidate.profile}</td>
                            <td className="px-6 py-2 whitespace-nowrap">
                              <div className="flex flex-wrap gap-1">
                                {candidate.skills.split(',').slice(0, 2).map((skill: string, index: number) => (
                                  <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                    {skill.trim()}
                                  </span>
                                ))}
                                {candidate.skills.split(',').length > 2 && (
                                  <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
                                    +{candidate.skills.split(',').length - 2}
                                  </span>
                                )}
                              </div>
                            </td>
                            <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-900">{candidate.peerReviewer}</td>
                            <td className="px-6 py-2 whitespace-nowrap">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(candidate.status)}`}>
                                {candidate.status}
                              </span>
                            </td>
                          </tr>
                        ))
                      )}
                    </tbody>
                  </table>
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="px-6 py-4 border-t border-gray-200">
                    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 bg-white p-2 rounded-md border border-gray-200">
                      <div className="text-xs sm:text-sm text-gray-600">
                        {filteredCandidates.length > 0 ? (
                          <>
                            Showing{" "}
                            {Math.min(
                              1 + (currentPage - 1) * itemsPerPage,
                              filteredCandidates.length
                            )}{" "}
                            to{" "}
                            {Math.min(currentPage * itemsPerPage, filteredCandidates.length)}{" "}
                            of {filteredCandidates.length} candidates
                          </>
                        ) : (
                          "No candidates found"
                        )}
                      </div>
                      <div className="w-full sm:w-auto flex justify-center sm:justify-end">
                        <AnimatedPagination
                          currentPage={currentPage}
                          totalPages={totalPages}
                          onPageChange={setCurrentPage}
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
