import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../index';
import { Job, FilterTag } from '../slices/jobsSlice';

// Base selectors
export const selectJobs = (state: RootState) => state.jobs.jobs;
export const selectJobsLoading = (state: RootState) => state.jobs.loading;
export const selectJobsError = (state: RootState) => state.jobs.error;
export const selectAppliedTags = (state: RootState) => state.jobs.appliedTags;
export const selectDateFilter = (state: RootState) => state.jobs.dateFilter;
export const selectAppliedCustomDateRange = (state: RootState) => state.jobs.appliedCustomDateRange;
export const selectCurrentPage = (state: RootState) => state.jobs.currentPage;
export const selectItemsPerPage = (state: RootState) => state.jobs.itemsPerPage;
export const selectSortConfig = (state: RootState) => state.jobs.sortConfig;
export const selectSearchTags = (state: RootState) => state.jobs.searchTags;

// Column definitions (moved from component)
const columns = [
  { key: "id", label: "Job ID", sortable: true },
  { key: "date_created", label: "Date Created", sortable: true },
  { key: "job_status", label: "Status", sortable: true },
  { key: "client", label: "Client", sortable: true },
  { key: "posted_by", label: "Posted By", sortable: true },
  { key: "recruiter", label: "Recruiter", sortable: true },
  { key: "role", label: "Role", sortable: true },
  { key: "no_of_positions", label: "Positions", sortable: true },
] as const;

// Memoized selector for filtered jobs
export const selectFilteredJobs = createSelector(
  [selectJobs, selectAppliedTags, selectDateFilter, selectAppliedCustomDateRange],
  (jobs, appliedTags, dateFilter, appliedCustomDateRange) => {
    const groupedTags = appliedTags.reduce((acc, tag) => {
      const key = tag.column;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(tag);
      return acc;
    }, {} as Record<string, FilterTag[]>);

    return jobs.filter((job) => {
      // Date Filter Logic
      const jobDate = new Date(job.date_created);
      if (isNaN(jobDate.getTime())) return false;

      if (dateFilter) {
        const now = new Date();
        const pastDate = new Date(now.setDate(now.getDate() - dateFilter));
        if (jobDate < pastDate) return false;
      }

      if (appliedCustomDateRange) {
        const startDate = new Date(appliedCustomDateRange.start);
        const endDate = new Date(appliedCustomDateRange.end);
        endDate.setDate(endDate.getDate() + 1);
        if (jobDate < startDate || jobDate >= endDate) return false;
      }

      // Tag-based Search Logic
      if (appliedTags.length > 0) {
        const matchesAllGroups = Object.values(groupedTags).every(tagGroup => {
          return tagGroup.some(tag => {
            const tagValue = tag.value.toLowerCase();
            const columnInfo = columns.find(c => c.label === tag.column);

            if (tag.column === 'Any') {
              return Object.values(job).some(val => String(val).toLowerCase().includes(tagValue));
            }
            if (!columnInfo) return false;

            const candidateValue = columnInfo.key === 'posted_by' ? job.management : job[columnInfo.key as keyof Job];
            return String(candidateValue).toLowerCase().includes(tagValue);
          });
        });

        if (!matchesAllGroups) {
          return false;
        }
      }

      return true;
    });
  }
);

// Memoized selector for sorted jobs
export const selectSortedJobs = createSelector(
  [selectFilteredJobs, selectSortConfig],
  (filteredJobs, sortConfig) => {
    return [...filteredJobs].sort((a, b) => {
      if (!sortConfig.key) return 0;

      let aValue: any;
      let bValue: any;

      if (sortConfig.key === "posted_by") {
        aValue = a.management;
        bValue = b.management;
      } else {
        aValue = a[sortConfig.key as keyof Job];
        bValue = b[sortConfig.key as keyof Job];
      }

      if (aValue < bValue) return sortConfig.direction === "ascending" ? -1 : 1;
      if (aValue > bValue) return sortConfig.direction === "ascending" ? 1 : -1;
      return 0;
    });
  }
);

// Memoized selector for paginated jobs
export const selectPaginatedJobs = createSelector(
  [selectSortedJobs, selectCurrentPage, selectItemsPerPage],
  (sortedJobs, currentPage, itemsPerPage) => {

    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const result = sortedJobs.slice(indexOfFirstItem, indexOfLastItem);
    return result;
  }
);

// Selector for search suggestions
export const selectSearchSuggestions = createSelector(
  [selectFilteredJobs, (_: RootState, inputValue: string) => inputValue],
  (filteredJobs, inputValue) => {
    if (!inputValue) return [];

    const suggestions: { value: string; column: string }[] = [];
    filteredJobs.forEach(job => {
      columns.forEach(col => {
        if (!col.key) return;
        const value = col.key === 'posted_by' ? job.management : job[col.key as keyof Job];
        if (typeof value === 'string' && value.toLowerCase().includes(inputValue.toLowerCase())) {
          if (!suggestions.some(s => s.value === value && s.column === col.label)) {
            suggestions.push({ value, column: col.label });
          }
        }
      });
    });
    return suggestions.slice(0, 7);
  }
);

// Selectors for unique values (for job assignments form)
export const selectUniqueClients = createSelector(
  [selectJobs],
  (jobs) => {
    const clients = jobs.map(job => job.client).filter(Boolean);
    return [...new Set(clients)].sort();
  }
);

export const selectUniqueRoles = createSelector(
  [selectJobs],
  (jobs) => {
    const roles = jobs.map(job => job.role).filter(Boolean);
    return [...new Set(roles)].sort();
  }
);

export const selectUniqueRecruiters = createSelector(
  [selectJobs],
  (jobs) => {
    // Handle multiple recruiters by splitting and flattening
    const allRecruiters = jobs
      .map(job => job.recruiter)
      .filter(Boolean)
      .flatMap(recruiter =>
        recruiter.includes(',')
          ? recruiter.split(',').map(r => r.trim())
          : [recruiter]
      );
    return [...new Set(allRecruiters)].sort();
  }
);

// Selectors for searchable suggestions
export const selectClientSuggestions = createSelector(
  [selectUniqueClients, (_: RootState, searchTerm: string) => searchTerm],
  (clients, searchTerm) => {
    if (!searchTerm) return clients.slice(0, 10);
    return clients
      .filter(client => client.toLowerCase().includes(searchTerm.toLowerCase()))
      .slice(0, 10);
  }
);

export const selectRoleSuggestions = createSelector(
  [selectUniqueRoles, (_: RootState, searchTerm: string) => searchTerm],
  (roles, searchTerm) => {
    if (!searchTerm) return roles.slice(0, 10);
    return roles
      .filter(role => role.toLowerCase().includes(searchTerm.toLowerCase()))
      .slice(0, 10);
  }
);

export const selectRecruiterSuggestions = createSelector(
  [selectUniqueRecruiters, (_: RootState, searchTerm: string) => searchTerm],
  (recruiters, searchTerm) => {
    if (!searchTerm) return recruiters.slice(0, 10);
    return recruiters
      .filter(recruiter => recruiter.toLowerCase().includes(searchTerm.toLowerCase()))
      .slice(0, 10);
  }
);

// Client company interface
export interface ClientCompany {
  id: string;
  name: string;
  status: "Active" | "Hold" | "Closed";
  jobCounts: {
    active: number;
    closed: number;
    onHold: number;
    total: number;
  };
}

// Selector for client companies with status
export const selectClientCompanies = createSelector(
  [selectJobs],
  (jobs): ClientCompany[] => {
    const clientMap = new Map<string, {
      name: string;
      activeJobs: number;
      closedJobs: number;
      onHoldJobs: number;
    }>();

    // Group jobs by client and count statuses (case-insensitive)
    jobs.forEach(job => {
      const clientName = job.client.trim();
      if (!clientName) return;

      // Use lowercase for key to handle case-insensitive grouping
      const clientKey = clientName.toLowerCase();

      if (!clientMap.has(clientKey)) {
        clientMap.set(clientKey, {
          name: clientName, // Keep original case for display
          activeJobs: 0,
          closedJobs: 0,
          onHoldJobs: 0,
        });
      } else {
        // Update name to use the most recent case variation
        const existing = clientMap.get(clientKey)!;
        existing.name = clientName;
      }

      const client = clientMap.get(clientKey)!;

      // Job status mapping
      switch (job.job_status?.toLowerCase()) {
        case 'active':
          client.activeJobs++;
          break;
        case 'closed':
          client.closedJobs++;
          break;
        case 'on hold':
        case 'onhold':
          client.onHoldJobs++;
          break;
        default:
          // Unknown job status
          break;
      }
    });

    // Convert to ClientCompany array with status logic
    const result = Array.from(clientMap.entries()).map(([clientName, data]) => {
      const totalJobs = data.activeJobs + data.closedJobs + data.onHoldJobs;

      let status: "Active" | "Hold" | "Closed";

      // Determine status based on new requirements:
      // Active: If there's at least one active job
      // Hold: If no active jobs but has on-hold jobs
      // Closed: If all jobs are closed (no active or on-hold jobs)
      if (data.activeJobs > 0) {
        // Has at least one active job
        status = "Active";
      } else if (data.onHoldJobs > 0) {
        // No active jobs but has on-hold jobs
        status = "Hold";
      } else {
        // All jobs are closed (or no jobs)
        status = "Closed";
      }

      return {
        id: clientName, // Use client name as ID for simplicity
        name: clientName,
        status,
        jobCounts: {
          active: data.activeJobs,
          closed: data.closedJobs,
          onHold: data.onHoldJobs,
          total: totalJobs,
        },
      };
    });

    // Processed clients from jobs
    return result;
  }
);

// Export columns for use in components
export { columns };
