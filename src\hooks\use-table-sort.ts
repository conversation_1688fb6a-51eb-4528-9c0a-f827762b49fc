import { useState } from 'react';

interface UseSortOptions<T> {
  /**
   * Initial sort field
   */
  initialSortField?: keyof T | null;
  
  /**
   * Initial sort direction
   */
  initialSortDirection?: 'asc' | 'desc';
  
  /**
   * Custom sort function
   */
  sortFn?: (a: T, b: T, field: keyof T, direction: 'asc' | 'desc') => number;
}

/**
 * Hook for handling table sorting with animations
 */
export function useTableSort<T extends Record<string, any>>({
  initialSortField = null,
  initialSortDirection = 'asc',
  sortFn,
}: UseSortOptions<T> = {}) {
  const [sortField, setSortField] = useState<keyof T | null>(initialSortField);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>(initialSortDirection);
  
  // Function to handle sort
  const handleSort = (field: keyof T) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };
  
  // Function to sort data
  const sortData = (data: T[]) => {
    if (!sortField) return data;
    
    return [...data].sort((a, b) => {
      if (sortFn) {
        return sortFn(a, b, sortField, sortDirection);
      }
      
      const aValue = a[sortField];
      const bValue = b[sortField];
      
      // Handle different types of values
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }
      
      if (aValue < bValue) {
        return sortDirection === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });
  };
  
  return {
    sortField,
    sortDirection,
    handleSort,
    sortData,
    setSortField,
    setSortDirection,
  };
}
