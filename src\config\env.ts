// Environment configuration
export const config = {
  // API Configuration - Use proxy in development, direct URL in production
  apiBaseUrl: import.meta.env.DEV ? '/api' : (import.meta.env.VITE_API_BASE_URL || 'http://142.93.222.128:8081'),

  // App Configuration
  appName: import.meta.env.VITE_APP_NAME || ' TalentTrack Pro',
  appVersion: import.meta.env.VITE_APP_VERSION || '1.0.0',
  appEnv: import.meta.env.VITE_APP_ENV || 'production',

  // Development flags
  isDevelopment: import.meta.env.DEV,
  isProduction: import.meta.env.PROD,
} as const;

// Helper function to build full API URLs
export const buildApiUrl = (endpoint: string): string => {
  return `${config.apiBaseUrl}${endpoint}`;
};

// Type definitions for better TypeScript support
export type Config = typeof config;
