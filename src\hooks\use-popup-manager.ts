import { createContext, useContext } from 'react';

interface PopupManagerContextType {
    openPopupId: string | null;
    setOpenPopupId: (id: string | null) => void;
}

const PopupManagerContext = createContext<PopupManagerContextType | undefined>(undefined);

export const usePopupManager = () => {
    const context = useContext(PopupManagerContext);
    if (context === undefined) {
        throw new Error('usePopupManager must be used within a PopupManagerProvider');
    }
    return context;
};

// Export the context for use in components
export { PopupManagerContext };
