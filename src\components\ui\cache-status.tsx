import { RefreshCw, Database, Clock } from 'lucide-react';
import { useCandidatesCacheStatus } from '@/contexts/candidates-context';

interface CacheStatusProps {
  showDetails?: boolean;
  className?: string;
}

export function CacheStatus({ showDetails = false, className = '' }: CacheStatusProps) {
  const { hasCache, isCacheValid, lastFetched, cacheAge } = useCandidatesCacheStatus();

  if (!hasCache || !lastFetched) {
    return null;
  }

  const formatCacheAge = (ageMs: number | null) => {
    if (!ageMs) return '';
    
    const minutes = Math.floor(ageMs / (1000 * 60));
    const seconds = Math.floor((ageMs % (1000 * 60)) / 1000);
    
    if (minutes > 0) {
      return `${minutes}m ${seconds}s ago`;
    }
    return `${seconds}s ago`;
  };

  return (
    <div className={`flex items-center gap-2 text-sm ${className}`}>
      <div className="flex items-center gap-1">
        <Database className={`h-4 w-4 ${isCacheValid ? 'text-green-500' : 'text-orange-500'}`} />
        <span className={isCacheValid ? 'text-green-700' : 'text-orange-700'}>
          {isCacheValid ? 'Cached' : 'Expired'}
        </span>
      </div>
      
      {showDetails && (
        <>
          <div className="flex items-center gap-1 text-gray-500">
            <Clock className="h-3 w-3" />
            <span className="text-xs">
              {formatCacheAge(cacheAge)}
            </span>
          </div>
          <span className="text-xs text-gray-400">
            ({lastFetched.toLocaleTimeString()})
          </span>
        </>
      )}
    </div>
  );
}

interface CacheRefreshButtonProps {
  onRefresh: () => Promise<void>;
  loading?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'ghost';
  className?: string;
}

export function CacheRefreshButton({ 
  onRefresh, 
  loading = false, 
  size = 'md',
  variant = 'primary',
  className = '' 
}: CacheRefreshButtonProps) {
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-2.5 text-base',
  };

  const variantClasses = {
    primary: 'bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white',
    secondary: 'bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white',
    ghost: 'bg-transparent hover:bg-gray-100 disabled:bg-gray-50 text-gray-700 border border-gray-300',
  };

  const iconSize = size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4';

  return (
    <button
      onClick={onRefresh}
      disabled={loading}
      className={`
        ${sizeClasses[size]} 
        ${variantClasses[variant]} 
        rounded-md font-medium flex items-center gap-2 transition-colors
        ${className}
      `}
      title="Refresh cached data"
    >
      <RefreshCw className={`${iconSize} ${loading ? 'animate-spin' : ''}`} />
      {size !== 'sm' && 'Refresh'}
    </button>
  );
}
