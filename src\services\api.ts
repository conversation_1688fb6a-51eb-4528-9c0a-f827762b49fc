// API service for HR Portal
import { config } from "@/config/env";

const API_BASE_URL = config.apiBaseUrl;

// Types for API requests and responses
export interface ApiRequest {
  user_id: string;
  user_type: string;
  user_name: string;
  page_no: number;
}

export interface ApiUser {
  id: number;
  name: string;
  user_type: string;
  email: string;
  user_name: string;
}

export interface ApiCandidate {
  id: number;
  job_id: number;
  name: string;
  mobile: string;
  email: string;
  client: string;
  current_company: string;
  position: string;
  profile: string;
  current_job_location: string;
  preferred_job_location: string;
  qualifications: string;
  experience: string;
  relevant_experience: string;
  current_ctc: string;
  expected_ctc: string;
  notice_period: string;
  linkedin: string;
  reason_for_job_change: string;
  holding_offer: string;
  recruiter: string;
  management: string | null;
  status: string;
  remarks: string;
  skills: string;
  serving_notice_period: string;
  period_of_notice: string;
  last_working_date: string | null;
  total_offers: number | null;
  highest_package_in_lpa: number | null;
  buyout: boolean;
  date_created: string;
  data_updated_date: string;
  resume_present: boolean;
  comments: Record<string, unknown>;
  peer_reviewer_level1: string | null;
  peer_reviewer_level2: string | null;
  remind_lwd: boolean;
  user_id: number;
  client_assigned: boolean;
}

export interface ApiResponse {
  candidates: ApiCandidate[];
  name: string;
  user: ApiUser;
}

// Types for Active Users API
export interface ActiveUser {
  client: string | null;
  created_by: string | null;
  deactivated_by: string | null;
  email: string;
  filename: string;
  id: number;
  image_deleted: boolean;
  is_active: boolean;
  is_verified: boolean;
  name: string;
  otp: string;
  password: string;
  peer_reviewer_status: boolean;
  registration_completed: string;
  user_type: string;
  username: string;
}

export interface ActiveUsersResponse {
  active_users_manager: ActiveUser[];
  active_users_recruiter: ActiveUser[];
}

// Request body for updating user status
export interface UpdateUserStatusRequest {
  user_name: string;
  new_status: boolean;
}

// Types for Peer Assigned Profiles API
export interface PeerAssignedCandidate {
  buyout: boolean;
  client: string;
  comments: Record<
    string,
    Array<{
      comment: string;
      timestamp: string;
    }>
  >;
  current_company: string;
  current_ctc: string;
  current_job_location: string;
  data_updated_date: string;
  date_created: string;
  email: string;
  expected_ctc: string;
  experience: string;
  highest_package_in_lpa: number | null;
  holding_offer: string;
  id: number;
  job_id: number;
  last_working_date: string | null;
  linkedin: string;
  management: string | null;
  mobile: string;
  name: string;
  notice_period: string;
  peer_reviewer_level1: string | null;
  peer_reviewer_level2: string | null;
  period_of_notice: string | null;
  position: string;
  preferred_job_location: string;
  profile: string;
  qualifications: string;
  reason_for_job_change: string;
  recruiter: string;
  relevant_experience: string;
  remarks: string;
  remind_lwd: boolean | null;
  resume_present: boolean;
  serving_notice_period: string;
  skills: string;
  status: string;
  total_offers: number | null;
  user_id: number;
}

export interface PeerAssignedProfilesResponse {
  candidates: PeerAssignedCandidate[];
}

export interface PeerAssignedProfilesRequest {
  recruiter: string;
}

// Types for Peer Approve Candidate API
export interface PeerApproveCandidateRequest {
  status: string | null;
  user_id: string;
  comments: Record<string, string> | null;
  candidate_id: number;
}

export interface PeerApproveCandidateResponse {
  status: string;
  message: string;
}

// Types for Recruiter Candidates API
export interface RecruiterCandidatesRequest {
  user_name: string;
}

export interface RecruiterCandidate {
  client: string;
  id: number;
  management: string | null;
  profile: string;
  recruiter: string;
  status: string;
  username: string;
}

export interface RecruiterCandidatesResponse {
  candidates: RecruiterCandidate[];
}

// Types for Candidate Analysis API
export interface CandidateAnalysisRequest {
  user_id: string;
  job_id: string;
  resume: string; // Base64 encoded resume
}

// Types for Job Posting API
export interface JobPostRequest {
  user_id: string;
  client: string;
  experience_min: number;
  experience_max: number;
  budget_min: number;
  budget_max: number;
  currency_type_min: string;
  currency_type_max: string;
  location: string;
  shift_timings: string;
  notice_period: string;
  role: string;
  detailed_jd: string;
  mode: string;
  job_status: string;
  skills: string;
  Job_Type: string;
  no_of_positions?: number;
  Job_Type_details?: string;
  Custom_Job_Type?: string;
  file_type?: string;
  country: string;
  post_to_careers?: boolean;
  recruiter: string[];
  jd_pdf?: string;
}

export interface JobPostResponse {
  status: string;
  message: string;
  job_post_id?: number;
}



export interface CandidateAnalysisResponse {
  analyze_candidate_profile_response: Array<{
    Experience: string;
    "Relevance Score": number;
    "Skill/Domain": string;
  }>;
  candidate_learning_response: {
    Certifications: string[];
    "Company Names": string[];
    "Skills/Domain": string[];
    "Technologies Used": Record<string, string[]>;
    "Working Periods": Record<string, unknown>;
  };
  candidate_learning_textual_representation: {
    BulletPoints: Record<string, string>;
    SummaryParagraph: string;
    Tags: string[];
  };
  career_progress_response: Array<{
    Company: string;
    "From Date": string;
    Location: string;
    Project: string;
    Title: string;
    "To Date": string;
    "Total Duration of Work": string;
  }>;
  expertise_response: {
    categories: Array<{
      Category: string;
      Items: string[];
    }>;
    domains: Array<{
      Description: string;
      Domain: string;
    }>;
  };
  job_info_response: {
    Candidate: string[];
    "Candidate Experience": string[];
    "Candidate Experience Percentage": string[];
    "Candidate Maximum Budget": string[];
    "Candidate Minimum Budget": string[];
    "Job Description Max Experience": string[];
    "Job Description Max Package (LPA)": string[];
    "Job Description Min Experience": string[];
    "Job Description Min Package (LPA)": string[];
    "Job Description Skills": string[];
    "Job Description Skills Count": string[];
    "Matching Skills": string[];
    "Resume Skills": string[];
    "Resume Skills Count": string[];
    "Skills Matching Percentage": string[];
  };
  user_id: string;
}

// Types for View All Jobs API
export interface Job {
  id: number;
  job_id: string;
  client: string;
  role: string;
  positions: number;
  status: string;
  posted_by: string;
  recruiter: string;
  date_posted: string;
  job_description?: string;
}

export interface EditJobRequest extends JobPostRequest {
  existing_file_ids?: number[];
  pdfs?: Array<{
    filename: string;
    file_data: string; // Base64 encoded
    extension: string;
  }>;
}

export interface EditJobResponse {
  status: string;
  message: string;
  job_post_id?: number;
  old_recruiter_usernames?: string[];
  new_recruiter_usernames?: string[];
  add_status?: string;
}

export interface DeleteJobResponse {
  status: string;
  message: string;
}

export interface UploadImageRequest {
  image: string; // Base64 encoded image data
  filename: string; // Original filename
  image_delete_status: boolean; // Image deletion status
}

export interface UploadImageResponse {
  message: string;
  success: boolean;
}

// Types for Edit Candidate API
export interface EditCandidateRequest {
  user_id: string;
  name: string;
  mobile: string;
  email: string;
  client: string;
  current_company: string;
  position: string;
  profile: string;
  current_job_location: string;
  preferred_job_location: string;
  qualifications: string;
  experience: string;
  relevant_experience: string;
  current_ctc: string;
  expected_ctc: string;
  reason_for_job_change: string;
  linkedin: string;
  remarks: string;
  skills: string;
  serving_notice_period: string;
  period_of_notice?: string;
  last_working_date?: string;
  buyout?: boolean;
  holding_offer: string;
  total_offers?: number;
  highest_package?: number;
  job_id: string;
  resume?: string; // Base64 encoded
  pdfs?: Array<{
    filename: string;
    data: string; // Base64 encoded
    extension: string;
  }>;
  remove_file_id?: number[];
}

export interface EditCandidateResponse {
  status: string;
  message: string;
}

export interface ViewResumeResponse {
  blob: Blob;
}

export interface UpdateCandidateRequest {
  user_id: string;
  peer_reviewer?: string;
  candidate_status: string;
  commentupdate?: Record<string, string>;
  priority?: 'High' | 'Low';
  peer_reviewer_email?: string;
}

export interface UpdateCandidateResponse {
  status: string;
  message: string;
  user_id: string;
  user_type: string;
  user_name: string;
  count_notification_no: number;
  career_count_notification_no: number;
  recruiter: string | null;
  management: string | null;
  recruiter_email: string | null;
  management_email: string | null;
  candidate_name: string;
  candidate_position: string;
  candidate_email: string;
  message_body: string;
}

export interface DeleteCandidateRequest {
  user_id: string;
}

export interface DeleteCandidateResponse {
  status: string;
  message: string;
}

// Types for Download Additional Files API
export interface DownloadAdditionalFilesRequest {
  candidate_id: number;
}

export interface DisableUserRequest {
  user_id: string;  // Changed to string
  user_status: boolean;
  user_name: string;
}

export interface DeactivateUserRequest {
  user_id: string;  // Changed to string
  user_name: string;
  user_status: boolean;
  deactivated_name: string;
}

export interface DeleteUserRequest {
  user_id: number;
  deleted_by: number;
}

export interface UpdatePeerReviewerRequest {
  user_id: number;
  status: boolean;
}

export interface UserManagementResponse {
  message?: string;
  messages?: string[];
  status: string;
  users?: ActiveUser[];
}

export interface CallMeetingRequest {
  name: string;
  purpose: string;
  mobile: string;
  date: string; // YYYY-MM-DD
  time: string; // HH:mm
  user_id: string;
  user_email: string;
}

export interface InPersonMeetingRequest {
  name: string;
  purpose: string;
  time: string; // HH:mm
  date: string; // YYYY-MM-DD
  email: string;
  location: string;
  user_id: string;
  user_email: string;
}

export interface GenericMeetingResponse {
  status: string;
  message?: string;
}

// Types for Resume Parsing API
export interface ResumeParseRequest {
  resume: string; // Base64 encoded resume
}

export interface ResumeParseResponse {
  name?: string;
  mail?: string;
  skill1?: string;
  phone?: string;
  current_company?: string;
  position?: string;
  current_job_location?: string;
  qualifications?: string;
  message?: string;
}

// Types for Candidate Registration API
export interface CheckCandidateRequest {
  name: string;
  mobile: string;
  email: string;
  total_experience_years: string;
  total_experience_months: string;
  relevant_experience_years: string;
  relevant_experience_months: string;
  qualifications: string;
  last_working_date: string;
  serving_notice_period: string;
}

export interface CheckCandidateResponse {
  jobIds?: number[];
  dates?: string[];
  clients?: string[];
  profiles?: string[];
  status?: string[];
}

export interface AddCandidateRequest {
  job_id: number;
  user_id: string;
  name: string;
  mobile: string;
  email: string;
  client: string;
  profile: string;
  skills: string;
  qualifications: string;
  current_ctc: string;
  expected_ctc: string;
  experience: string;
  relevant_experience: string;
  reason_for_job_change?: string;
  current_company?: string;
  current_job_position?: string;
  current_job_location?: string;
  preferred_job_location?: string;
  notice_period?: string;
  joining_offer?: string;
  linkedin_url?: string;
  remarks?: string;
  serving_notice_period?: string;
  period_of_notice?: string;
  last_working_date?: string;
  buyout?: boolean;
  holding_offer?: string;
  resume: string; // Base64 encoded
  pdfs?: Array<{
    data: string; // Base64 encoded
    extension: string;
    filename: string;
  }>;
}

export interface AddCandidateResponse {
  status: string;
  message?: string;
  candidate_id?: number;
  date_created?: string;
}

export interface TeamsMeetingRequest {
  subject: string;
  attendees: string[];
  cc_recipients: string[];
  recruiter_email: string;
  start_date: string;
  end_date: string;
  start_time: string;
  end_time: string;
  time_zone: string;
  description: string;
  files?: Array<{
    file_name: string;
    file_content_base64: string;
  }>;
}

export interface ZoomMeetingRequest {
  subject: string;
  description: string;
  start_date: string;
  start_time: string;
  end_time: string;
  timezone: string;
  recruiter_email: string;
  recruiter_id: number;
  attendees: string[];
  cc_recipients: string[];
  files?: Array<{
    file_name: string;
    file_content_base64: string;
  }>;
}

export interface MeetingResponse {
  status: string;
  message?: string;
}

// Interface for meeting data from backend
export interface MeetingData {
  meeting_id: string;
  event_id: string;
  recruiter_id: string;
  subject: string;
  start_date: string;
  start_time: string;
  end_date: string;
  end_time: string;
  email: string;
  attendees: string;
  cc_recipients: string;
  time_zone: string;
  join_url: string;
  description: string;
  meeting_type: string;
  uploaded_files: string;
}

export interface GetAllMeetingsResponse {
  meetings: MeetingData[];
  message?: string;
}

// Interface for call events data from backend
export interface CallEventData {
  id: number;
  event_id?: string;
  user_id: number;
  name: string;
  purpose: string;
  mobile: string;
  date: string;
  time: string;
  email?: string;
  user_email?: string;
  location?: string;
  meeting_type: string;
  candidate_reply: string;
  rescheduled_date?: string | null;
  created_at?: string;
  status?: string;
}

export interface GetAllCallEventsResponse {
  call_events?: CallEventData[];
  message?: string;
}

// Type for direct array response
export type CallEventsDirectResponse = CallEventData[];

// Interface for zoom meetings data from backend
export interface ZoomMeetingData {
  id: number;
  meeting_id?: string;
  event_id?: string;
  subject: string;
  description: string;
  start_date: string;
  end_date?: string;
  start_time: string;
  end_time: string;
  timezone?: string;
  time_zone?: string;
  recruiter_email: string;
  recruiter_id: number;
  attendees: string[] | string;
  cc_recipients: string[] | string;
  join_url?: string;
  meeting_type?: string;
  created_at?: string;
  status?: string;
}

export interface GetZoomMeetingsResponse {
  zoom_meetings?: ZoomMeetingData[];
  meetings?: ZoomMeetingData[];
  message?: string;
}

// Types for Help Support API
export interface IssueReportRequest {
  screenshots: string[]; // Base64 encoded images
  issue_title: string;
  issue_description: string;
  issue_type: string;
  severity: string;
  module_name: string;
  userId: string;
}

export interface IssueReportResponse {
  status: string;
  message?: string;
}


// Types for Email Sharing API
export interface EmailShareRequest {
  user_id: string;
  emailSubject: string;
  selectedDetails: Array<{
    jobDetails: Record<string, any>;
  }>;
  emailBody: string;
  recipientEmails: string[];
  selectedCandidate: any[];
}

export interface EmailShareResponse {
  auth_url: string;
  status?: string;
  message?: string;
}

export interface MatchCandidatesRequest {
  selectedCandidate: any[];
  recruiterEmail: string;
  recruiterName: string;
}

export interface MatchCandidatesResponse {
  status: string;
  message: string;
}

// API service class
export class ApiService {
  private static async makeRequest<T>(
    endpoint: string,
    data: ApiRequest
  ): Promise<T> {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      // API request failed
      throw error;
    }
  }

  // Fetch candidates from the API
  static async fetchCandidates(
    userId: string,
    userType: string,
    userName: string,
    pageNo: number = 1
  ): Promise<ApiResponse> {
    const requestData: ApiRequest = {
      user_id: userId,
      user_type: userType,
      user_name: userName,
      page_no: pageNo,
    };

    const response = await this.makeRequest<ApiResponse>(
      "/redisdashboard",
      // "/dashboard",

      requestData
    );

    return response;
  }

  // Fetch active users from the API
  static async fetchActiveUsers(): Promise<ActiveUsersResponse> {
    const body = {
      user_name: localStorage.getItem("userName") || "",
      new_status: false,
    };
    try {
      const response = await fetch(`${API_BASE_URL}/active_users`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      // Failed to fetch active users
      throw error;
    }
  }

  // Update user status (verify, deactivate, peer reviewer)
  static async updateUserStatus(
    userName: string,
    newStatus: boolean,
    statusType: "verify" | "deactivate" | "peer_reviewer"
  ): Promise<unknown> {
    try {
      const requestBody: UpdateUserStatusRequest = {
        user_name: userName,
        new_status: newStatus,
      };

      // Determine the endpoint based on status type
      let endpoint = "";
      switch (statusType) {
        case "verify":
          endpoint = "/disable_user";
          break;
        case "deactivate":
          endpoint = "/deactivate_user";
          break;
        case "peer_reviewer":
          endpoint = "/update_peer_reviewer_status";
          break;
        default:
          throw new Error("Invalid status type");
      }

      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      // Failed to update user status
      throw error;
    }
  }

  // Fetch peer assigned profiles from the API
  static async fetchPeerAssignedProfiles(
    recruiterName: string
  ): Promise<PeerAssignedProfilesResponse> {
    try {
      const requestBody: PeerAssignedProfilesRequest = {
        recruiter: recruiterName,
      };

      const response = await fetch(
        `${API_BASE_URL}/candidates_pending_peer_approval`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      // Failed to fetch peer assigned profiles
      throw error;
    }
  }

  // Approve/Reject peer assigned candidate
  static async peerApproveCandidate(
    requestData: PeerApproveCandidateRequest
  ): Promise<PeerApproveCandidateResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/peer_approve_candidate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: PeerApproveCandidateResponse = await response.json();
      return result;
    } catch (error) {
      // Failed to approve/reject candidate
      throw error;
    }
  }

  // Fetch recruiter's candidates from the API
  static async fetchRecruiterCandidates(
    recruiterName: string
  ): Promise<RecruiterCandidate[]> {
    try {
      const requestBody: RecruiterCandidatesRequest = {
        user_name: recruiterName,
      };

      const response = await fetch(`${API_BASE_URL}/get_recruiters_candidate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: RecruiterCandidate[] = await response.json();
      return result;
    } catch (error) {
      // Failed to fetch recruiter candidates
      throw error;
    }
  }

  // Analyze candidate profile
  static async analyzeCandidateProfile(
    userId: string,
    jobId: string,
    resumeBase64: string
  ): Promise<CandidateAnalysisResponse> {
    try {
      const requestBody: CandidateAnalysisRequest = {
        user_id: userId,
        job_id: jobId,
        resume: resumeBase64,
      };

      const response = await fetch(`${API_BASE_URL}/candidate_over_view`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: CandidateAnalysisResponse = await response.json();
      return result;
    } catch (error) {
      // Failed to analyze candidate profile
      throw error;
    }
  }

  // Analyze FETCH JOB DESCRIPTION
  static async fetchJobDescription(jobId: number): Promise<Blob> {
    try {
      const response = await fetch(`${API_BASE_URL}/view_jd/${jobId}`, {
        method: "GET",
      });

      if (!response.ok) {
        // If the server sends a JSON error response, try to parse it
        try {
          const errorData = await response.json();
          throw new Error(
            `HTTP error! status: ${response.status}, message: ${errorData.message}`
          );
        } catch (e) {
          // Fallback if the error response is not JSON
          throw new Error(`HTTP error! status: ${response.status}`);
        }
      } // The response from this endpoint is a file (blob), not JSON

      const blob = await response.blob();
      return blob;
    } catch (error) {
      // Failed to fetch job description
      throw error;
    }
  }

  // FETCH A CANDIDATE'S RESUME ==
  static async fetchResume(candidateId: number): Promise<Blob> {
    try {
      const response = await fetch(
        `${API_BASE_URL}/view_resume/${candidateId}`,
        {
          method: "GET",
        }
      );

      if (!response.ok) {
        // Try to get a meaningful error message from the response body
        const errorMessage = await response.text();
        throw new Error(
          `HTTP error! status: ${response.status}, message: ${errorMessage}`
        );
      }

      // The response is the resume file (blob)
      const blob = await response.blob();
      return blob;
    } catch (error) {
      // Failed to fetch resume
      throw error;
    }
  }

  static async postJob(jobData: JobPostRequest): Promise<JobPostResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/post_job`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(jobData),
      });

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (e) {
          // If error response is not JSON, use the default message
        }
        throw new Error(errorMessage);
      }

      const result: JobPostResponse = await response.json();
      return result;
    } catch (error) {
      // Failed to post job
      throw error;
    }
  }

  static async editJob(jobId: number, jobData: EditJobRequest): Promise<EditJobResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/edit_job_post/${jobId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(jobData),
      });

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (e) {
          // If error response is not JSON, use the default message
        }
        throw new Error(errorMessage);
      }

      const result: EditJobResponse = await response.json();
      return result;
    } catch (error) {
      throw error;
    }
  }

  // Delete Job Post
  static async deleteJob(jobId: number): Promise<DeleteJobResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/delete_job_post/${jobId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (e) {
          // If error response is not JSON, use the default message
        }
        throw new Error(errorMessage);
      }

      const result: DeleteJobResponse = await response.json();
      return result;
    } catch (error) {
      throw error;
    }
  }

  // Download Job Description (already exists but updating for consistency)
  static async downloadJobDescription(jobId: number): Promise<void> {
    try {
      const blob = await this.fetchJobDescription(jobId);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `job_description_${jobId}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

    } catch (error) {
      // Failed to download job description
      throw error;
    }
  }

  static async getUserImage(userId: string): Promise<string | null> {
    try {
      const response = await fetch(`${API_BASE_URL}/user_image/${userId}`, {
        method: "GET",
      });
      if (response.status === 404) return null;
      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);
      const blob = await response.blob();
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      // Failed to fetch user image
      return null;
    }
  }

  static async uploadUserImage(
    userId: string,
    imageData: string,
    filename?: string
  ): Promise<UploadImageResponse> {
    try {
      const base64Data = imageData.includes(",")
        ? imageData.split(",")[1]
        : imageData;
      const requestBody: UploadImageRequest = {
        image: base64Data,
        filename: filename || `profile_${userId}_${Date.now()}.jpg`,
        image_delete_status: false,
      };

      const response = await fetch(`${API_BASE_URL}/upload_user_image/${userId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      // Failed to upload user image
      throw error;
    }
  }

  static async editCandidate(
    candidateId: number,
    candidateData: EditCandidateRequest
  ): Promise<EditCandidateResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/edit_candidate/${candidateId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(candidateData),
      });

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.error_message || errorData.message || errorMessage;
        } catch (e) {
          // If error response is not JSON, use the default message
        }
        throw new Error(errorMessage);
      }

      const result: EditCandidateResponse = await response.json();
      return result;
    } catch (error) {
      throw error;
    }
  }

  static async viewResume(candidateId: number): Promise<Blob> {
    try {
      const response = await fetch(`${API_BASE_URL}/view_resume/${candidateId}`, {
        method: "GET",
      });

      if (!response.ok) {
        const errorMessage = await response.text();
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorMessage}`);
      }

      const blob = await response.blob();
      return blob;
    } catch (error) {
      // Failed to view resume
      throw error;
    }
  }

  // Update Candidate Status
  static async updateCandidate(
    candidateId: number,
    updateData: UpdateCandidateRequest
  ): Promise<UpdateCandidateResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/update_candidate/${candidateId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (e) {
          // If error response is not JSON, use the default message
        }
        throw new Error(errorMessage);
      }

      const result: UpdateCandidateResponse = await response.json();
      return result;
    } catch (error) {
      throw error;
    }
  }

  // Delete Candidate
  static async deleteCandidate(
    candidateId: number,
    userId: string
  ): Promise<DeleteCandidateResponse> {
    try {
      const requestData: DeleteCandidateRequest = {
        user_id: userId,
      };

      const response = await fetch(`${API_BASE_URL}/delete_candidate/${candidateId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (e) {
          // If error response is not JSON, use the default message
        }
        throw new Error(errorMessage);
      }

      const result: DeleteCandidateResponse = await response.json();
      return result;
    } catch (error) {
      throw error;
    }
  }

  // Download Additional Files
  static async downloadAdditionalFiles(candidateId: number): Promise<Blob> {
    try {
      const requestData: DownloadAdditionalFilesRequest = {
        candidate_id: candidateId,
      };

      const response = await fetch(`${API_BASE_URL}/download_additional_files`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (e) {
          // If error response is not JSON, use the default message
        }
        throw new Error(errorMessage);
      }

      const blob = await response.blob();
      return blob;
    } catch (error) {
      // Failed to download additional files
      throw error;
    }
  }

  // Helper method to download resume directly
  static async downloadResume(candidateId: number, candidateName: string): Promise<void> {
    try {
      const blob = await this.viewResume(candidateId);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // Determine file extension based on blob type
      const fileExtension = blob.type.includes('pdf') ? 'pdf' : 'doc';
      link.download = `resume_${candidateName.replace(/\s+/g, '_')}_${candidateId}.${fileExtension}`;

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

    } catch (error) {
      // Failed to download resume
      throw error;
    }
  }

  // Helper method to download additional files directly
  static async downloadAdditionalFilesZip(candidateId: number, candidateName: string): Promise<void> {
    try {
      const blob = await this.downloadAdditionalFiles(candidateId);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `additional_files_${candidateName.replace(/\s+/g, '_')}_${candidateId}.zip`;

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

    } catch (error) {
      // Failed to download additional files
      throw error;
    }
  }

  // Create a Call Meeting event
  static async createCallEvent(data: CallMeetingRequest): Promise<GenericMeetingResponse> {
    const response = await fetch(`${API_BASE_URL}/create_call_event`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  // Create an In-Person Meeting event
  static async createInPersonEvent(data: InPersonMeetingRequest): Promise<GenericMeetingResponse> {
    const response = await fetch(`${API_BASE_URL}/create_in_person_event`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  static async disableUser(
    userId: string,
    userName: string,
    userStatus: boolean
  ): Promise<UserManagementResponse> {
    try {
      const requestBody: DisableUserRequest = {
        user_id: userId,
        user_name: userName,
        user_status: userStatus,
      };

      const response = await fetch(`${API_BASE_URL}/disable_user`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: UserManagementResponse = await response.json();
      return result;
    } catch (error) {
      throw error;
    }
  }

  // Deactivate/Activate User (Active functionality)
  static async deactivateUser(
    userId: string,
    userName: string,
    userStatus: boolean,
    deactivatedName: string
  ): Promise<UserManagementResponse> {
    try {
      const requestBody: DeactivateUserRequest = {
        user_id: userId,
        user_name: userName,
        user_status: userStatus,
        deactivated_name: deactivatedName,
      };

      const response = await fetch(`${API_BASE_URL}/deactivate_user`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: UserManagementResponse = await response.json();
      return result;
    } catch (error) {
      throw error;
    }
  }

  // Update Peer Reviewer Status
  static async updatePeerReviewerStatus(
    userId: number,
    status: boolean
  ): Promise<UserManagementResponse> {
    try {
      const requestBody: UpdatePeerReviewerRequest = {
        user_id: userId,
        status: status,
      };

      const response = await fetch(`${API_BASE_URL}/update_peer_reviewer_status`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: UserManagementResponse = await response.json();
      return result;
    } catch (error) {
      throw error;
    }
  }

  // Delete User
  static async deleteUser(
    userId: number,
    deletedById: number
  ): Promise<UserManagementResponse> {
    try {
      const requestBody: DeleteUserRequest = {
        user_id: userId,
        deleted_by: deletedById,
      };

      const response = await fetch(`${API_BASE_URL}/delete_user`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: UserManagementResponse = await response.json();
      return result;
    } catch (error) {
      // Failed to delete user
      throw error;
    }
  }

  // Assign candidates to recruiter
  static async assignCandidatesToRecruiter(payload: any): Promise<any> {
    try {
      const response = await fetch(`${API_BASE_URL}/assign_candidate_new_recuriter`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      throw error;
    }
  }

  // Parse Resume
  static async parseResume(resumeBase64: string): Promise<ResumeParseResponse> {
    try {
      const requestBody: ResumeParseRequest = {
        resume: resumeBase64,
      };

      const response = await fetch(`${API_BASE_URL}/parse_resume`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (e) {
          // If error response is not JSON, use the default message
        }
        throw new Error(errorMessage);
      }

      const result: ResumeParseResponse = await response.json();
      return result;
    } catch (error) {
      // Failed to parse resume
      throw error;
    }
  }

  // Check if candidate exists
  static async checkCandidate(candidateData: CheckCandidateRequest): Promise<CheckCandidateResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/check_candidate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(candidateData),
      });

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (e) {
          // If error response is not JSON, use the default message
        }
        throw new Error(errorMessage);
      }

      const result: CheckCandidateResponse = await response.json();
      return result;
    } catch (error) {
      // Failed to check candidate
      throw error;
    }
  }

  // Add new candidate
  static async addCandidate(candidateData: AddCandidateRequest): Promise<AddCandidateResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/add_candidate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(candidateData),
      });

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (e) {
          // If error response is not JSON, use the default message
        }
        throw new Error(errorMessage);
      }

      const result: AddCandidateResponse = await response.json();
      return result;
    } catch (error) {
      // Failed to add candidate
      throw error;
    }
  }
  static async createTeamsMeeting(meetingData: TeamsMeetingRequest): Promise<MeetingResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/create_event`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(meetingData),
      });
      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorData.message || errorMessage;
        } catch (e) {
          // If error response is not JSON, use the default message
        }
        throw new Error(errorMessage);
      }
      const result: MeetingResponse = await response.json();
      return result;
    } catch (error) {
      throw error;
    }
  }

  // Create Zoom Meeting
  static async createZoomMeeting(meetingData: ZoomMeetingRequest): Promise<MeetingResponse> {
    console.log('📡 API Call: createZoomMeeting with data:', meetingData);
    try {
      const response = await fetch(`${API_BASE_URL}/schedule_zoom_meeting`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(meetingData),
      });
      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorData.message || errorMessage;
        } catch (e) {
          // If error response is not JSON, use the default message
        }
        throw new Error(errorMessage);
      }
      const result: MeetingResponse = await response.json();
      console.log('✅ createZoomMeeting response:', result);
      return result;
    } catch (error) {
      console.error('❌ createZoomMeeting failed:', error);
      throw error;
    }
  }

  // Submit Issue Report
  static async submitIssueReport(issueData: IssueReportRequest): Promise<IssueReportResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/auth_issue_report`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(issueData),
      });
      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorData.message || errorMessage;
        } catch (e) {
          // If error response is not JSON, use the default message
        }
        throw new Error(errorMessage);
      }
      const result: IssueReportResponse = await response.json();
      return result;
    } catch (error) {
      throw error;
    }
  }

  // Fetch all meetings
  static async fetchAllMeetings(recruiterId: string): Promise<GetAllMeetingsResponse> {
    console.log('📡 API Call: fetchAllMeetings with recruiterId:', recruiterId);
    try {
      const response = await fetch(`${API_BASE_URL}/get_all_meetings`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ recruiter_id: recruiterId }),
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result: GetAllMeetingsResponse = await response.json();
      console.log('✅ fetchAllMeetings response:', result);
      return result;
    } catch (error) {
      console.error('❌ fetchAllMeetings failed:', error);
      throw error;
    }
  }

  // Fetch all call events for a user
  static async fetchAllCallEvents(userId: string): Promise<GetAllCallEventsResponse | CallEventsDirectResponse> {
    console.log('📡 API Call: fetchAllCallEvents with userId:', userId);
    try {
      const response = await fetch(`${API_BASE_URL}/get_all_call_events/${userId}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.json();
      console.log('✅ fetchAllCallEvents response:', result);
      return result;
    } catch (error) {
      console.error('❌ fetchAllCallEvents failed:', error);
      throw error;
    }
  }

  // Fetch all zoom meetings
  static async fetchZoomMeetings(): Promise<GetZoomMeetingsResponse> {
    console.log('📡 API Call: fetchZoomMeetings');
    try {
      // Get user ID from localStorage for zoom meetings
      const userId = localStorage.getItem('userId') || '';
      console.log('📡 fetchZoomMeetings - userId from localStorage:', userId);

      // Create URL with query parameters for GET request
      const url = userId ? `${API_BASE_URL}/get_zoom_meetings?user_id=${encodeURIComponent(userId)}` : `${API_BASE_URL}/get_zoom_meetings`;

      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result: GetZoomMeetingsResponse = await response.json();
      console.log('✅ fetchZoomMeetings response:', result);
      return result;
    } catch (error) {
      console.error('❌ fetchZoomMeetings failed:', error);
      throw error;
    }
  }

  // Assign client to job
  static async assignClientToJob(payload: { id: number; job_id: number; client: string; profile: string }): Promise<any> {
    try {
      const response = await fetch(`${API_BASE_URL}/assign_client`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      throw error;
    }
  }

    static async initiateEmailShare(data: EmailShareRequest): Promise<EmailShareResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/login`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: EmailShareResponse = await response.json();
      return result;
    } catch (error) {
      throw error;
    }
  }

  // Match candidates to jobs after email authentication
  static async matchCandidatesToJobs(data: MatchCandidatesRequest): Promise<MatchCandidatesResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/match_candidates_to_jobs`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const result: MatchCandidatesResponse = await response.json();
      return result;
    } catch (error) {
      throw error;
    }
  }

}

export function convertApiCandidateToLocal(apiCandidate: ApiCandidate) {
  return {
    id: apiCandidate.id,
    jobId: apiCandidate.job_id?.toString() || "N/A",
    firstName: apiCandidate.name?.split(" ")[0] || "",
    lastName: apiCandidate.name?.split(" ").slice(1).join(" ") || "",
    email: apiCandidate.email || "",
    phone: apiCandidate.mobile || "",
    client: apiCandidate.client || "N/A",
    profile: apiCandidate.profile || "N/A",
    skills: apiCandidate.skills || "",
    status: apiCandidate.status || "",
    appliedDate: apiCandidate.date_created || "",
    source: "API", // Default value since not provided in API
    experience: parseFloat(apiCandidate.experience || "0") || 0,
    education: apiCandidate.qualifications || "",
    location: apiCandidate.preferred_job_location || "",
    salary: `${apiCandidate.current_ctc || "N/A"} - ${apiCandidate.expected_ctc || "N/A"}`,
    notes: apiCandidate.remarks || "",
    lastUpdated: apiCandidate.data_updated_date || "",
    comment: apiCandidate.reason_for_job_change || "",
    peerReviewer:
      apiCandidate.peer_reviewer_level1 ||
      apiCandidate.peer_reviewer_level2 ||
      "",
    recruiter: apiCandidate.recruiter || "",
    management: apiCandidate.management || null,
    client_assigned: apiCandidate.client_assigned || false,
  };
}

