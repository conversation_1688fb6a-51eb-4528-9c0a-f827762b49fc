import { createSelector } from '@reduxjs/toolkit';
import type { RootState } from '@/store';

// Base selectors
export const selectPeerAssignedProfiles = (state: RootState) => state.peerAssignedProfiles.profiles;
export const selectPeerAssignedProfilesLoading = (state: RootState) => state.peerAssignedProfiles.loading;
export const selectPeerAssignedProfilesError = (state: RootState) => state.peerAssignedProfiles.error;
export const selectPeerAssignedProfilesLastFetched = (state: RootState) => state.peerAssignedProfiles.lastFetched;

// Memoized selectors
export const selectPeerAssignedProfilesCount = createSelector(
  [selectPeerAssignedProfiles],
  (profiles) => profiles.length
);

export const selectPendingProfiles = createSelector(
  [selectPeerAssignedProfiles],
  (profiles) => profiles.filter(profile => profile.status === 'PR-Pending')
);

export const selectApprovedProfiles = createSelector(
  [selectPeerAssignedProfiles],
  (profiles) => profiles.filter(profile => profile.status === 'PR-Approved')
);

export const selectRejectedProfiles = createSelector(
  [selectPeerAssignedProfiles],
  (profiles) => profiles.filter(profile => profile.status === 'PR-Rejected')
);

export const selectProfileById = createSelector(
  [selectPeerAssignedProfiles, (state: RootState, profileId: number) => profileId],
  (profiles, profileId) => profiles.find(profile => profile.id === profileId)
);
