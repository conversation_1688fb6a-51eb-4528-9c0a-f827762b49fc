import { useState, useCallback } from 'react';
import { useAnimation } from 'framer-motion';

interface UseTableAnimationOptions {
  /**
   * Initial loading state
   */
  initialLoading?: boolean;
}

/**
 * Hook to handle table animations
 */
export function useTableAnimation({
  initialLoading = false,
}: UseTableAnimationOptions = {}) {
  const [isLoading, setIsLoading] = useState(initialLoading);
  const tableControls = useAnimation();
  
  /**
   * Function to animate table loading state
   */
  const animateLoading = useCallback(async (loadingFn: () => Promise<any>) => {
    setIsLoading(true);
    
    // Animate table out
    await tableControls.start({
      opacity: 0.7,
      y: 10,
      transition: { duration: 0.3 }
    });
    
    try {
      // Execute the loading function
      const result = await loadingFn();
      return result;
    } finally {
      setIsLoading(false);
      
      // Animate table back in
      tableControls.start({
        opacity: 1,
        y: 0,
        transition: { 
          duration: 0.5, 
          type: "spring", 
          stiffness: 100,
          damping: 15
        }
      });
    }
  }, [tableControls]);
  
  /**
   * Function to animate table sorting
   */
  const animateSorting = useCallback(async () => {
    // Apply a small shake animation to the table when sorting
    await tableControls.start({
      x: [0, -3, 3, -2, 2, 0],
      transition: { duration: 0.4 }
    });
  }, [tableControls]);
  
  /**
   * Function to animate table pagination
   */
  const animatePagination = useCallback(async () => {
    // Apply a fade transition when changing pages
    await tableControls.start({
      opacity: 0.5,
      y: 5,
      transition: { duration: 0.2 }
    });
    
    // Animate back in
    await tableControls.start({
      opacity: 1,
      y: 0,
      transition: { duration: 0.3 }
    });
  }, [tableControls]);
  
  return {
    isLoading,
    setIsLoading,
    tableControls,
    animateLoading,
    animateSorting,
    animatePagination,
  };
}
