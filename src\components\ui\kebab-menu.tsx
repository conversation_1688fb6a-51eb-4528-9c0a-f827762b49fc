import React, { useState, useRef, useEffect } from "react";
import { MoreVertical } from "lucide-react";
import { cn } from "@/lib/utils";

export interface KebabMenuItem {
  id: string;
  label: string;
  icon?: React.ComponentType<{ className?: string }>;
  onClick: () => void;
  variant?: "default" | "destructive" | "warning";
  disabled?: boolean;
  separator?: boolean; // Add separator after this item
  hasSubmenu?: boolean; // Add this property to indicate submenu
  submenuItems?: {
    id: string;
    label: string;
    action: () => void;
    icon?: React.ComponentType<{ className?: string }>;
  }[];
}

interface KebabMenuProps {
  items: KebabMenuItem[];
  className?: string;
  buttonClassName?: string;
  menuClassName?: string;
  disabled?: boolean;
}

export function KebabMenu({
  items,
  className,
  buttonClassName,
  menuClassName,
  disabled = false,
}: KebabMenuProps) {
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Close menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      return () => document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [isOpen]);

  // Close menu on escape key
  useEffect(() => {
    function handleEscape(event: KeyboardEvent) {
      if (event.key === "Escape") {
        setIsOpen(false);
      }
    }

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      return () => document.removeEventListener("keydown", handleEscape);
    }
  }, [isOpen]);

  const handleItemClick = (item: KebabMenuItem) => {
    if (!item.disabled) {
      item.onClick();
      setIsOpen(false);
    }
  };

  const getItemVariantClasses = (variant?: string) => {
    switch (variant) {
      case "destructive":
        return "text-red-600 hover:bg-red-50 hover:text-red-700";
      case "warning":
        return "text-amber-600 hover:bg-amber-50 hover:text-amber-700";
      default:
        return "text-gray-700 hover:bg-[var(--secondary)] hover:text-[var(--primary-dark)]";
    }
  };

  const getIconColor = (variant?: string) => {
    switch (variant) {
      case "destructive":
        return "text-red-500";
      case "warning":
        return "text-amber-500";
      default:
        return "text-[var(--primary)]";
    }
  };

  return (
    <div className={cn("relative inline-block", className)}>
      {/* Kebab Menu Button */}
      <button
        ref={buttonRef}
        onClick={() => setIsOpen(!isOpen)}
        disabled={disabled}
        className={cn(
          "inline-flex items-center justify-center w-8 h-8 rounded-md transition-all duration-200",
          "text-[var(--muted-foreground)] hover:text-[var(--primary)] hover:bg-[var(--secondary)]",
          "focus:outline-none focus:ring-2 focus:ring-[var(--ring)] focus:ring-offset-1",
          "disabled:opacity-50 disabled:cursor-not-allowed",
          isOpen && "bg-[var(--secondary)] text-[var(--primary)]",
          buttonClassName
        )}
        aria-label="More actions"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <MoreVertical className="h-4 w-4" />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div
          ref={menuRef}
          className={cn(
            "absolute right-0 top-full mt-1 z-[9999]",
            "bg-white rounded-lg shadow-xl border border-[var(--border)]",
            "min-w-[160px] py-1",
            "animate-in fade-in-0 zoom-in-95 duration-200",
            menuClassName
          )}
          role="menu"
          aria-orientation="vertical"
        >
          {items.map((item, index) => (
            <React.Fragment key={item.id}>
              <button
                onClick={() => handleItemClick(item)}
                disabled={item.disabled}
                className={cn(
                  "w-full flex items-center gap-3 px-3 py-2 text-sm font-medium",
                  "transition-colors duration-150 text-left",
                  "disabled:opacity-50 disabled:cursor-not-allowed",
                  getItemVariantClasses(item.variant)
                )}
                role="menuitem"
              >
                {item.icon && (
                  <item.icon className={cn("h-4 w-4 flex-shrink-0", getIconColor(item.variant))} />
                )}
                <span className="flex-1">{item.label}</span>
              </button>

              {/* Separator */}
              {item.separator && index < items.length - 1 && (
                <div className="my-1 border-t border-[var(--border)]" />
              )}
            </React.Fragment>
          ))}
        </div>
      )}
    </div>
  );
}

// Utility function to create common menu items
export const createKebabMenuItem = (
  id: string,
  label: string,
  onClick: () => void,
  options?: {
    icon?: React.ComponentType<{ className?: string }>;
    variant?: "default" | "destructive" | "warning";
    disabled?: boolean;
    separator?: boolean;
    hasSubmenu?: boolean;
    submenuItems?: {
      id: string;
      label: string;
      action: () => void;
      icon?: React.ComponentType<{ className?: string }>;
    }[];
  }
): KebabMenuItem => ({
  id,
  label,
  onClick,
  ...options,
});

export default KebabMenu;
