import { useState, useEffect, useRef } from "react";
import { toast } from "react-toastify";
import { Search, FileText, Users, Plus, Info, X, Check } from "lucide-react";
import {
  KebabMenu,
  createKebabMenuItem,
  type KebabMenuItem,
} from "@/components/ui/kebab-menu";
import { AnimatedTableWrapper } from "@/components/ui/animated-table-wrapper";
import { TableSkeleton } from "@/components/ui/table-skeleton";
import { useDebounce } from "@/hooks/use-debounce";
import { AnimatedTableRow } from "@/components/ui/animated-table-row";
import { AnimatedSortIcon } from "@/components/ui/animated-sort-icon";
import { Pagination } from "@/components/ui/pagination";
import { useTableAnimation } from "@/hooks/use-table-animation";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { fetchJobs } from "@/store/slices/jobsSlice";
import {
  selectJobs,
  selectJobsLoading,
  selectJobsError,
} from "@/store/selectors/jobsSelectors";

// Define the Requirement type based on Job interface
interface Requirement {
  id: number;
  job_id: string;
  client: string;
  role: string;
  positions: number;
  status: string;
  posted_by: string;
  recruiter: string;
  date_posted: string;
  job_description?: string;
}

// Define FilterTag interface for advanced search
interface FilterTag {
  value: string;
  column: string;
}

// Define column configuration
interface Column {
  key: keyof Requirement;
  label: string;
  sortable: boolean;
}

export function AssignedRequirements() {
  return (
    <div className="h-full flex flex-col">
      <div className="bg-white rounded-lg shadow border border-gray-200 p-4 flex-1">
        <div className="flex-1 flex flex-col">
          <RequirementsTable />
        </div>
      </div>
    </div>
  );
}

function RequirementsTable() {
  const columns: Column[] = [
    { key: "date_posted", label: "Job Posted", sortable: true },
    { key: "job_id", label: "Job Id", sortable: true },
    { key: "status", label: "Job Status", sortable: true },
    { key: "client", label: "Client", sortable: true },
    { key: "posted_by", label: "Posted By", sortable: true },
    { key: "role", label: "Role", sortable: true },
    { key: "positions", label: "No of Positions", sortable: true },
  ];

  // Create kebab menu items for each requirement
  const createRequirementMenuItems = (
    requirement: Requirement
  ): KebabMenuItem[] => [
      createKebabMenuItem(
        "view-jd",
        "View Job Description",
        () =>
          toast.info(
            `View JD for ${requirement.job_id} functionality to be implemented`
          ),
        { icon: FileText }
      ),
      createKebabMenuItem(
        "resume-match",
        "Resume Match",
        () =>
          toast.info(
            `Resume Match for ${requirement.job_id} functionality to be implemented`
          ),
        { icon: Users }
      ),
      createKebabMenuItem(
        "add-candidate",
        "Add Candidate",
        () =>
          toast.info(
            `Add Candidate for ${requirement.job_id} functionality to be implemented`
          ),
        { icon: Plus, separator: true }
      ),
      createKebabMenuItem(
        "view-details",
        "View Details",
        () =>
          toast.info(
            `View Details for ${requirement.job_id} functionality to be implemented`
          ),
        { icon: Info }
      ),
    ];

  // Animation controls
  const { isLoading, animateSorting, animatePagination } = useTableAnimation();

  // Redux state
  const dispatch = useAppDispatch();
  const allJobs = useAppSelector(selectJobs);
  const apiLoading = useAppSelector(selectJobsLoading);
  const apiError = useAppSelector(selectJobsError);

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // State for advanced search and filters
  const [searchTags, setSearchTags] = useState<FilterTag[]>([]);
  const [appliedTags, setAppliedTags] = useState<FilterTag[]>([]);
  const [inputValue, setInputValue] = useState("");
  const debouncedInputValue = useDebounce(inputValue, 250);
  const [statusFilter, setStatusFilter] = useState("");

  // Refs for dropdown management
  const searchContainerRef = useRef<HTMLDivElement>(null);

  // State for sorting
  const [sortConfig, setSortConfig] = useState<{
    key: keyof Requirement | null;
    direction: "ascending" | "descending" | null;
  }>({
    key: null,
    direction: null,
  });

  const currentRecruiterName = localStorage.getItem("name") || "";

  // Fetch jobs data on component mount
  useEffect(() => {
    if (currentRecruiterName) {
      dispatch(fetchJobs({ username: currentRecruiterName }));
    }
  }, [dispatch, currentRecruiterName]);

  // Filter jobs assigned to current recruiter first
  const assignedJobs = allJobs.filter((job) => {
    return job.recruiter.split(", ").includes(currentRecruiterName);
  });

  // Convert Job to Requirement format
  const assignedRequirements: Requirement[] = assignedJobs.map((job) => ({
    id: job.id,
    job_id: job.id.toString(),
    client: job.client,
    role: job.role,
    positions: parseInt(job.no_of_positions) || 0,
    status: job.job_status,
    posted_by: job.management,
    recruiter: job.recruiter,
    date_posted: job.date_created,
    job_description: job.detailed_jd,
  }));

  // Generate search suggestions based on input
  const getSearchSuggestions = (
    input: string
  ): { value: string; column: string }[] => {
    if (!input.trim()) return [];

    const suggestions: { value: string; column: string }[] = [];
    const inputLower = input.toLowerCase();

    // Get unique values from requirements for suggestions
    const uniqueValues = new Map<string, string>();

    assignedRequirements.forEach((req) => {
      Object.entries(req).forEach(([key, value]) => {
        if (
          typeof value === "string" &&
          value.toLowerCase().includes(inputLower)
        ) {
          const columnLabel =
            columns.find((col) => col.key === key)?.label || key;
          uniqueValues.set(`${columnLabel}:${value}`, columnLabel);
        }
      });
    });

    Array.from(uniqueValues.entries())
      .slice(0, 10)
      .forEach(([key, column]) => {
        const value = key.split(":")[1];
        suggestions.push({ value, column });
      });

    return suggestions;
  };

  const suggestions = getSearchSuggestions(debouncedInputValue);

  // Handler functions for search functionality
  const handleAddTag = (
    tagOrSuggestion: string | { value: string; column: string }
  ) => {
    let newTag: FilterTag;

    if (typeof tagOrSuggestion === "string") {
      newTag = { value: tagOrSuggestion.trim(), column: "Any" };
    } else {
      newTag = {
        value: tagOrSuggestion.value.trim(),
        column: tagOrSuggestion.column,
      };
    }

    if (
      newTag.value &&
      !searchTags.some(
        (t) => t.value === newTag.value && t.column === newTag.column
      )
    ) {
      setSearchTags([...searchTags, newTag]);
    }
    setInputValue("");
  };

  const handleRemoveTag = (tagToRemove: FilterTag) => {
    setSearchTags(
      searchTags.filter(
        (tag) =>
          tag.value !== tagToRemove.value || tag.column !== tagToRemove.column
      )
    );
  };

  const handleApplyFilters = () => {
    setAppliedTags([...searchTags]);
    setCurrentPage(1);
  };

  const handleClearAllFilters = () => {
    setSearchTags([]);
    setAppliedTags([]);
    setInputValue("");
    setCurrentPage(1);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  // Effect to close suggestions on outside click
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        searchContainerRef.current &&
        !searchContainerRef.current.contains(event.target as Node)
      ) {
        setInputValue("");
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Filter requirements based on applied search tags and status
  const filteredRequirements = assignedRequirements.filter((requirement) => {
    // Check if requirement matches applied search tags
    const matchesTags =
      appliedTags.length === 0 ||
      appliedTags.some((tag) => {
        if (tag.column === "Any") {
          // Search across all fields
          return Object.values(requirement).some((val) =>
            String(val).toLowerCase().includes(tag.value.toLowerCase())
          );
        } else {
          // Search in specific column
          const columnKey = columns.find(
            (col) => col.label === tag.column
          )?.key;
          if (columnKey && requirement[columnKey]) {
            return String(requirement[columnKey])
              .toLowerCase()
              .includes(tag.value.toLowerCase());
          }
          return false;
        }
      });

    const matchesStatus =
      statusFilter === "" || requirement.status === statusFilter;

    return matchesTags && matchesStatus;
  });

  // Sort requirements if sort config is set
  const sortedRequirements = [...filteredRequirements].sort((a, b) => {
    if (!sortConfig.key) return 0;

    const aValue = a[sortConfig.key];
    const bValue = b[sortConfig.key];

    // Handle undefined values
    if (aValue === undefined && bValue === undefined) return 0;
    if (aValue === undefined) return 1;
    if (bValue === undefined) return -1;

    if (aValue < bValue) {
      return sortConfig.direction === "ascending" ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortConfig.direction === "ascending" ? 1 : -1;
    }
    return 0;
  });

  // Get current requirements for pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentRequirements = sortedRequirements.slice(
    indexOfFirstItem,
    indexOfLastItem
  );

  // Change page with animation
  const paginate = async (pageNumber: number) => {
    await animatePagination();
    setCurrentPage(pageNumber);
  };

  // Handle sort with animation
  const handleSort = async (key: keyof Requirement) => {
    // Apply a small shake animation to the table when sorting
    await animateSorting();

    let direction: "ascending" | "descending" | null = "ascending";

    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    } else if (
      sortConfig.key === key &&
      sortConfig.direction === "descending"
    ) {
      direction = null;
    }

    setSortConfig({ key, direction });
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800";
      case "Hold":
        return "bg-yellow-100 text-yellow-800";
      case "Closed":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Show loading state
  if (apiLoading) {
    return (
      <div className="flex flex-col">
        <div className="mb-4 flex flex-wrap justify-between items-start gap-4">
          <div className="flex-1 min-w-[300px] relative">
            <div className="flex flex-wrap items-center gap-2 p-2 pr-20 border border-gray-300 rounded-md bg-gray-50">
              <Search className="h-5 w-5 text-gray-400 flex-shrink-0" />
              <input
                type="text"
                className="flex-grow min-w-[150px] h-[24px] p-1 text-sm bg-transparent outline-none"
                placeholder="Search and add filters..."
                disabled
              />
            </div>
          </div>
          <div className="flex items-center gap-3">
            <select
              className="bg-gray-50 border border-gray-300 text-gray-500 text-sm rounded-md p-2.5"
              disabled
            >
              <option>All Status</option>
            </select>
            <select
              className="bg-gray-50 border border-gray-300 text-gray-500 text-sm rounded-md p-2.5"
              disabled
            >
              <option>10 per page</option>
            </select>
          </div>
        </div>
        <TableSkeleton rows={8} cols={columns.length + 1} />
      </div>
    );
  }

  // Show error state
  if (apiError) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <p className="text-red-600 mb-4">{apiError}</p>
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="flex flex-col">
      {/* Advanced Search Bar */}
      <div className="mb-4 flex flex-wrap justify-between items-start gap-4">
        {/* Search Tags & Input */}
        <div
          className="flex-1 min-w-[300px] relative z-[11]"
          ref={searchContainerRef}
        >
          <div className="flex flex-wrap items-center gap-2 p-2 pr-20 border border-gray-300 rounded-md bg-white focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500">
            <Search className="h-5 w-5 text-gray-400 flex-shrink-0" />
            {searchTags.map((tag) => (
              <span
                key={`${tag.column}-${tag.value}`}
                className="inline-flex items-center mr-2 px-3 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
              >
                <span className="font-normal text-blue-600 mr-1">
                  {tag.column}:
                </span>
                {tag.value}
                <button
                  onClick={() => handleRemoveTag(tag)}
                  className="ml-1.5 -mr-1 p-0.5 rounded-full text-blue-500 hover:bg-blue-200"
                >
                  <X className="h-3.5 w-3.5" />
                </button>
              </span>
            ))}
            <input
              type="text"
              className="flex-grow min-w-[150px] h-[24px] p-1 text-sm bg-transparent outline-none"
              placeholder="Search and add filters..."
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={(e) => {
                if (e.key === "Enter" && inputValue.trim()) {
                  e.preventDefault();
                  handleAddTag({ value: inputValue, column: "Any" });
                }
              }}
            />
          </div>
          <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center">
            <button
              onClick={handleApplyFilters}
              title="Apply Filters"
              className="p-1.5 rounded-full hover:bg-blue-100 text-blue-600 focus:outline-none"
            >
              <Check className="h-5 w-5" />
            </button>
            <button
              onClick={handleClearAllFilters}
              title="Clear All Filters"
              className="p-1.5 rounded-full hover:bg-gray-200 text-gray-500 focus:outline-none"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          {suggestions.length > 0 && (
            <div className="absolute z-[9999] w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
              {suggestions.map((s, i) => (
                <div
                  key={`${s.value}-${i}`}
                  className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center justify-between"
                  onClick={() => handleAddTag(s)}
                >
                  <span className="text-sm">{s.value}</span>
                  <span className="text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded">
                    {s.column}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Filters */}
        <div className="flex items-center gap-3">
          {/* Status Filter */}
          <select
            className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md p-2.5 focus:ring-blue-500 focus:border-blue-500"
            onChange={(e) => setStatusFilter(e.target.value)}
            value={statusFilter}
          >
            <option value="">All Status</option>
            <option value="Active">Active</option>
            <option value="Hold">Hold</option>
            <option value="Closed">Closed</option>
          </select>

          {/* Page Size Selector */}
          <select
            className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md p-2.5 focus:ring-blue-500 focus:border-blue-500"
            onChange={(e) => setItemsPerPage(Number(e.target.value))}
            value={itemsPerPage}
          >
            <option value="10">10 per page</option>
            <option value="20">20 per page</option>
            <option value="50">50 per page</option>
            <option value="100">100 per page</option>
          </select>
        </div>
      </div>

      {/* Table with fixed header and scrollable body */}
      <AnimatedTableWrapper
        isLoading={isLoading}
        loadingComponent={<TableSkeleton rows={8} cols={columns.length + 1} />}
        className="border border-gray-200 rounded-md overflow-hidden flex-1"
      >
        <div
          className="overflow-auto h-[490px] table-scrollbar"
          style={{ scrollbarGutter: "stable" }}
        >
          <table
            className="min-w-full divide-y divide-gray-200"
            style={{ minWidth: "1275px" }}
          >
            <thead className="bg-gray-50 sticky top-0 z-10">
              <tr>
                {columns.map((column) => (
                  <th
                    key={String(column.key)}
                    scope="col"
                    className={`sticky top-0 z-10 px-3 py-2 text-left text-[11px] font-semibold text-gray-500 uppercase tracking-wider cursor-pointer bg-gray-50 border-b border-gray-200 ${column.key === "date_posted"
                      ? "w-28"
                      : column.key === "job_id"
                        ? "w-24"
                        : column.key === "status"
                          ? "w-24"
                          : column.key === "client"
                            ? "w-35"
                            : column.key === "role"
                              ? "w-45"
                              : column.key === "positions"
                                ? "w-28"
                                : "w-24"
                      }`}
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className="flex items-center gap-1">
                      {column.label}
                      <AnimatedSortIcon
                        direction={
                          sortConfig.key === column.key
                            ? sortConfig.direction
                            : null
                        }
                        active={sortConfig.key === column.key}
                        size={14}
                      />
                    </div>
                  </th>
                ))}
                {/* Action Column Header */}
                <th className="sticky top-0 z-10 px-6 py-3 text-center text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200 w-16">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentRequirements.length > 0 ? (
                currentRequirements.map((requirement, index) => (
                  <AnimatedTableRow key={requirement.id} index={index}>
                    {columns.map((column) => (
                      <td
                        key={`${requirement.id}-${String(column.key)}`}
                        className="px-3 py-2 text-xs text-gray-800 font-medium whitespace-nowrap"
                      >
                        {column.key === "status" ? (
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-[11px] font-bold ${getStatusColor(
                              requirement.status
                            )}`}
                          >
                            {requirement.status}
                          </span>
                        ) : (
                          String(requirement[column.key])
                        )}
                      </td>
                    ))}

                    {/* Actions Column */}
                    <td className="px-2 py-2 whitespace-nowrap text-xs text-gray-500 w-16">
                      <div className="flex items-center justify-center">
                        <KebabMenu
                          items={createRequirementMenuItems(requirement)}
                        />
                      </div>
                    </td>
                  </AnimatedTableRow>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={
                      columns.length + 1
                    } /* Add 1 for the actions column */
                    className="px-3 py-3 text-center text-xs text-gray-500"
                  >
                    No requirements found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </AnimatedTableWrapper>

      {/* Pagination */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mt-2 text-xs text-gray-700 gap-4 bg-white p-2 rounded-md border border-gray-200">
        <div className="text-xs sm:text-sm">
          Showing {indexOfFirstItem + 1} to{" "}
          {Math.min(indexOfLastItem, filteredRequirements.length)} of{" "}
          {filteredRequirements.length} requirements
        </div>
        <div className="w-full sm:w-auto flex justify-center sm:justify-end">
          <Pagination
            currentPage={currentPage}
            totalPages={Math.ceil(filteredRequirements.length / itemsPerPage)}
            onPageChange={paginate}
            className="flex items-center gap-1"
          />
        </div>
      </div>
    </div>
  );
}
