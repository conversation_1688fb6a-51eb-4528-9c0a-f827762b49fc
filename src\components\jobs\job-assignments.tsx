import { useState, useEffect } from "react";
import { toast } from "react-toastify";
import { useUser } from "@/contexts/user-context";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { fetchJobs } from "@/store/slices/jobsSlice";
import {
    selectJobs,
    selectJobsLoading,
    selectJobsError,
    selectUniqueClients,
    selectUniqueRoles,
    selectUniqueRecruiters,
} from "@/store/selectors/jobsSelectors";
import { SearchableDropdown } from "@/components/ui/searchable-dropdown";
import { ApiService, JobPostRequest } from "@/services/api";
import { Building2, Briefcase, Clock, Users, FileText, Upload } from "lucide-react";

// Job types as per requirements
const jobTypes = [
    "Permanent with Client",
    "Permanent with Makonis",
    "Contract",
    "Custom",
];

const modesOfWork = ["Hybrid", "WFO", "WFH"];
const shiftTimings = ["General", "Rotational"];
const jobStatuses = ["Active", "Close", "Hold"];

// Currency options for budget
const currencies = [
    { code: "INR", label: "INR (LPA)", symbol: "₹" },
    { code: "USD", label: "USD", symbol: "$" },
    { code: "EUR", label: "EUR", symbol: "€" },
    { code: "CAD", label: "CAD", symbol: "C$" },
];

// Common countries
const countries = [
    "India",
    "United States",
    "United Kingdom",
    "Canada",
    "Australia",
    "Germany",
    "France",
    "Singapore",
    "UAE",
    "Netherlands",
    "Switzerland",
    "Sweden",
    "Norway",
    "Denmark",
    "Other",
];

// Interface for form data
interface JobFormData {
    client: string;
    role: string;
    skills: string;
    country: string;
    place: string;
    modeOfWork: string;
    maxExperience: string;
    minExperience: string;
    maxBudget: string;
    minBudget: string;
    currency: string;
    noticePeriod: string;
    jobType: string;
    numberOfPositions: string;
    shiftTimings: string;
    jobStatus: string;
    recruiter: string;
    detailedJD: File | File[] | null;
    jobDescription: string;
    post_to_careers: boolean;
    contract_in_months: string;
    Custom_Job_Type: string;
    Job_Type_details: string;
}

export function JobAssignments() {
    const { userName, userEmail } = useUser();
    const dispatch = useAppDispatch();

    // Redux state
    const jobs = useAppSelector(selectJobs);
    const loading = useAppSelector(selectJobsLoading);
    const error = useAppSelector(selectJobsError);

    // Get all unique values for suggestions
    const allClients = useAppSelector(selectUniqueClients);
    const allRoles = useAppSelector(selectUniqueRoles);
    const allRecruiters = useAppSelector(selectUniqueRecruiters);

    const [isSubmitting, setIsSubmitting] = useState(false);

    // State for form data
    const [formData, setFormData] = useState<JobFormData>({
        client: "",
        role: "",
        skills: "",
        country: "",
        place: "",
        modeOfWork: "",
        maxExperience: "",
        minExperience: "",
        maxBudget: "",
        minBudget: "",
        currency: "INR",
        noticePeriod: "",
        jobType: "",
        numberOfPositions: "",
        shiftTimings: "",
        jobStatus: "",
        recruiter: "",
        detailedJD: null,
        jobDescription: "",
        post_to_careers: false,
        contract_in_months: "",
        Custom_Job_Type: "",
        Job_Type_details: "",
    });

    // Fetch jobs data on component mount
    useEffect(() => {
        if (jobs.length === 0) {
            dispatch(fetchJobs({ username: userName || userEmail || "managerone" }));
        }
    }, [dispatch, userName, userEmail, jobs.length]);

    const handleInputChange = (
        e: React.ChangeEvent<
            HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
        >
    ) => {
        const { name, value, type } = e.target;
        const checked = (e.target as HTMLInputElement).checked;

        setFormData({
            ...formData,
            [name]: type === 'checkbox' ? checked : value,
        });
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files.length > 0) {
            if (e.target.files.length === 1) {
                // Single file
                setFormData({
                    ...formData,
                    detailedJD: e.target.files[0],
                });
            } else {
                // Multiple files
                setFormData({
                    ...formData,
                    detailedJD: Array.from(e.target.files),
                });
            }
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        // Validate conditional fields
        if (formData.jobType === "Contract" && !formData.contract_in_months) {
            toast.error("Contract in Months is required when Job Type is Contract");
            return;
        }

        if (formData.jobType === "Custom") {
            if (!formData.Custom_Job_Type) {
                toast.error("Custom Job Type is required when Job Type is Custom");
                return;
            }
            if (!formData.Job_Type_details) {
                toast.error("Job Type Details is required when Job Type is Custom");
                return;
            }
        }

        try {
            // Prepare file data
            let jd_pdf: string | undefined = undefined;
            let pdfs: Array<{data: string; extension: string; filename: string}> | undefined = undefined;

            if (formData.detailedJD) {
                if (Array.isArray(formData.detailedJD)) {
                    // Multiple files
                    pdfs = await Promise.all(
                        formData.detailedJD.map(async (file) => {
                            const base64 = await convertFileToBase64(file);
                            const extension = file.name.split('.').pop() || '';
                            return {
                                data: base64,
                                extension: extension,
                                filename: file.name
                            };
                        })
                    );
                } else {
                    // Single file
                    jd_pdf = await convertFileToBase64(formData.detailedJD);
                }
            }

            // Prepare submission data
            const submissionData: JobPostRequest = {
                user_id: localStorage.getItem("user_id") || userName || userEmail || "",
                client: formData.client,
                experience_min: parseInt(formData.minExperience) || 0,
                experience_max: parseInt(formData.maxExperience) || 0,
                budget_min: parseInt(formData.minBudget) || 0,
                budget_max: parseInt(formData.maxBudget) || 0,
                currency_type_min: formData.currency,
                currency_type_max: formData.currency,
                location: formData.place,
                shift_timings: formData.shiftTimings,
                notice_period: formData.noticePeriod,
                role: formData.role,
                detailed_jd: formData.jobDescription,
                mode: formData.modeOfWork,
                job_status: formData.jobStatus,
                skills: formData.skills,
                Job_Type: formData.jobType,
                no_of_positions: parseInt(formData.numberOfPositions) || undefined,
                country: formData.country,
                recruiter: [formData.recruiter],
                jd_pdf,
                pdfs,
                post_to_careers: formData.post_to_careers,
                contract_in_months:  formData.contract_in_months,
                Custom_Job_Type:  formData.Custom_Job_Type ,
                Job_Type_details: formData.Job_Type_details ,
            };

            console.log("Form submitted:", submissionData);
            toast.success("Job posted successfully!");

        } catch (error) {
            console.error("Error submitting form:", error);
            toast.error("Failed to submit job. Please try again.");
        }
    };

    // Helper function to convert file to base64
    const convertFileToBase64 = (file: File): Promise<string> => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => {
                const result = reader.result as string;
                // Remove the data:application/pdf;base64, prefix
                const base64 = result.split(',')[1];
                resolve(base64);
            };
            reader.onerror = error => reject(error);
        });
    };

    if (loading) {
        return (
            <div className="w-full max-h-[calc(100vh-150px)] bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Loading job data...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="w-full max-h-[calc(100vh-150px)] bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <p className="text-red-600 mb-4">Error loading job data: {error}</p>
                    <button
                        onClick={() => window.location.reload()}
                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                        Retry
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="h-full bg-gray-50 flex flex-col">
            <div className="flex-shrink-0 bg-gray-50 px-4 py-6">
                <div className="max-w-6xl mx-auto">
                    <h1 className="text-3xl font-bold text-gray-900">Job Assignments</h1>
                    <p className="text-gray-600 mt-1">Create and assign new job positions</p>
                </div>
            </div>

            <div className="flex-1 overflow-auto px-4 pb-6">
                <div className="max-w-6xl mx-auto">

                    <form onSubmit={handleSubmit} className="space-y-4">
                        {/* Section 1: Job Details - Light Blue Background */}
                        <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                            <div className="flex items-center mb-3">
                                <Building2 className="h-5 w-5 text-blue-600 mr-2" />
                                <h2 className="text-lg font-semibold text-blue-800">Job Details</h2>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                                <div>
                                    <label className="block text-sm font-medium text-blue-700 mb-1">
                                        Client <span className="text-red-500">*</span>
                                    </label>
                                    <SearchableDropdown
                                        label=""
                                        name="client"
                                        value={formData.client}
                                        onChange={(value) => setFormData({ ...formData, client: value })}
                                        suggestions={allClients}
                                        placeholder="Search for existing client or enter new"
                                        required={true}
                                        isNewMode={false}
                                        onToggleNewMode={() => { }}
                                        newModeText=""
                                        selectExistingText=""
                                        addNewText=""
                                        simpleMode={true}
                                        borderColor="blue"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-blue-700 mb-1">
                                        Role <span className="text-red-500">*</span>
                                    </label>
                                    <SearchableDropdown
                                        label=""
                                        name="role"
                                        value={formData.role}
                                        onChange={(value) => setFormData({ ...formData, role: value })}
                                        suggestions={allRoles}
                                        placeholder="Search for existing role or enter new"
                                        required={true}
                                        isNewMode={false}
                                        onToggleNewMode={() => { }}
                                        newModeText=""
                                        selectExistingText=""
                                        addNewText=""
                                        simpleMode={true}
                                        borderColor="blue"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-blue-700 mb-1">
                                        Skills <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                        type="text"
                                        name="skills"
                                        required
                                        className="w-full px-3 py-2.5 border border-blue-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        value={formData.skills}
                                        onChange={handleInputChange}
                                        placeholder="e.g., JavaScript, React, Node.js"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-blue-700 mb-1">
                                        No Of Positions <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                        type="number"
                                        name="numberOfPositions"
                                        required
                                        min="1"
                                        className="w-full px-3 py-2.5 border border-blue-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        value={formData.numberOfPositions}
                                        onChange={handleInputChange}
                                        placeholder="Number of positions"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-blue-700 mb-1">
                                        Country <span className="text-red-500">*</span>
                                    </label>
                                    <select
                                        name="country"
                                        required
                                        className="w-full px-3 py-2.5 border border-blue-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        value={formData.country}
                                        onChange={handleInputChange}
                                    >
                                        <option value="">Select Country</option>
                                        {countries.map((country, index) => (
                                            <option key={index} value={country}>
                                                {country}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-blue-700 mb-1">
                                        Place <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                        type="text"
                                        name="place"
                                        required
                                        className="w-full px-3 py-2.5 border border-blue-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        value={formData.place}
                                        onChange={handleInputChange}
                                        placeholder="e.g., Mumbai, New York, Remote"
                                    />
                                </div>
                            </div>
                        </div>

                        {/* Section 2: Work & Experience - Light Green Background */}
                        <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                            <div className="flex items-center mb-3">
                                <Briefcase className="h-5 w-5 text-green-600 mr-2" />
                                <h2 className="text-lg font-semibold text-green-800">Work & Experience</h2>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                                <div>
                                    <label className="block text-sm font-medium text-green-700 mb-1">
                                        Job Type <span className="text-red-500">*</span>
                                    </label>
                                    <select
                                        name="jobType"
                                        required
                                        className="w-full px-3 py-2.5 border border-green-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                        value={formData.jobType}
                                        onChange={handleInputChange}
                                    >
                                        <option value="">Select Job Type</option>
                                        {jobTypes.map((type, index) => (
                                            <option key={index} value={type}>
                                                {type}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-green-700 mb-1">
                                        Mode of Work <span className="text-red-500">*</span>
                                    </label>
                                    <select
                                        name="modeOfWork"
                                        required
                                        className="w-full px-3 py-2.5 border border-green-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                        value={formData.modeOfWork}
                                        onChange={handleInputChange}
                                    >
                                        <option value="">Select Mode of Work</option>
                                        {modesOfWork.map((mode, index) => (
                                            <option key={index} value={mode}>
                                                {mode}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-green-700 mb-1">
                                        Min Experience <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                        type="number"
                                        name="minExperience"
                                        required
                                        min="0"
                                        step="0.5"
                                        className="w-full px-3 py-2.5 border border-green-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                        value={formData.minExperience}
                                        onChange={handleInputChange}
                                        placeholder="Years"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-green-700 mb-1">
                                        Max Experience <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                        type="number"
                                        name="maxExperience"
                                        required
                                        min="0"
                                        step="0.5"
                                        className="w-full px-3 py-2.5 border border-green-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                        value={formData.maxExperience}
                                        onChange={handleInputChange}
                                        placeholder="Years"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-green-700 mb-1">
                                        Currency <span className="text-red-500">*</span>
                                    </label>
                                    <select
                                        name="currency"
                                        required
                                        className="w-full px-3 py-2.5 border border-green-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                        value={formData.currency}
                                        onChange={handleInputChange}
                                    >
                                        {currencies.map((currency) => (
                                            <option key={currency.code} value={currency.code}>
                                                {currency.label}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-green-700 mb-1">
                                        Min Budget <span className="text-red-500">*</span>
                                    </label>
                                    <div className="relative">
                                        <span className="absolute left-3 top-2 text-gray-500">
                                            {currencies.find((c) => c.code === formData.currency)?.symbol || "₹"}
                                        </span>
                                        <input
                                            type="number"
                                            name="minBudget"
                                            required
                                            min="0"
                                            className="w-full pl-8 pr-3 py-2.5 border border-green-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                            value={formData.minBudget}
                                            onChange={handleInputChange}
                                            placeholder="Numeric"
                                        />
                                    </div>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-green-700 mb-1">
                                        Max Budget <span className="text-red-500">*</span>
                                    </label>
                                    <div className="relative">
                                        <span className="absolute left-3 top-2 text-gray-500">
                                            {currencies.find((c) => c.code === formData.currency)?.symbol || "₹"}
                                        </span>
                                        <input
                                            type="number"
                                            name="maxBudget"
                                            required
                                            min="0"
                                            className="w-full pl-8 pr-3 py-2.5 border border-green-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                            value={formData.maxBudget}
                                            onChange={handleInputChange}
                                            placeholder="Numeric"
                                        />
                                    </div>

                                    {/* Post to Careers Checkbox */}
                                    <div className="mt-3 flex items-center">
                                        <input
                                            type="checkbox"
                                            id="post_to_careers"
                                            name="post_to_careers"
                                            checked={formData.post_to_careers}
                                            onChange={handleInputChange}
                                            className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                                        />
                                        <label htmlFor="post_to_careers" className="ml-2 text-sm text-green-700">
                                            Post to Makonis Career Page
                                        </label>
                                    </div>
                                </div>
                            </div>

                            {/* Conditional fields based on Job Type */}
                            {formData.jobType === "Contract" && (
                                <div className="mt-4">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                        <div>
                                            <label className="block text-sm font-medium text-green-700 mb-1">
                                                Contract in Months <span className="text-red-500">*</span>
                                            </label>
                                            <input
                                                type="number"
                                                name="contract_in_months"
                                                required
                                                min="1"
                                                className="w-full px-3 py-2.5 border border-green-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                                value={formData.contract_in_months}
                                                onChange={handleInputChange}
                                                placeholder="Enter number of months"
                                            />
                                        </div>
                                    </div>
                                </div>
                            )}

                            {formData.jobType === "Custom" && (
                                <div className="mt-4">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                      

                                        <div>
                                            <label className="block text-sm font-medium text-green-700 mb-1">
                                                  Custom Job Type <span className="text-red-500">*</span>
                                            </label>
                                            <input
                                                type="text"
                                                name="Job_Type_details"
                                                required
                                                className="w-full px-3 py-2.5 border border-green-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                                value={formData.Job_Type_details}
                                                onChange={handleInputChange}
                                                placeholder="Enter job type details"
                                            />
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Section 3: Timings & Period - Light Purple Background */}
                        <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
                            <div className="flex items-center mb-3">
                                <Clock className="h-5 w-5 text-purple-600 mr-2" />
                                <h2 className="text-lg font-semibold text-purple-800">Timings & Period</h2>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <div>
                                    <label className="block text-sm font-medium text-purple-700 mb-1">
                                        Shift Timings <span className="text-red-500">*</span>
                                    </label>
                                    <select
                                        name="shiftTimings"
                                        required
                                        className="w-full px-3 py-2.5 border border-purple-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                        value={formData.shiftTimings}
                                        onChange={handleInputChange}
                                    >
                                        <option value="">Select Shift Timings</option>
                                        {shiftTimings.map((shift, index) => (
                                            <option key={index} value={shift}>
                                                {shift}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-purple-700 mb-1">
                                        Notice Period <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                        type="text"
                                        name="noticePeriod"
                                        required
                                        className="w-full px-3 py-2.5 border border-purple-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                        value={formData.noticePeriod}
                                        onChange={handleInputChange}
                                        placeholder="e.g., 30 days, 60 days"
                                    />
                                </div>
                            </div>
                        </div>

                        {/* Section 4: Status & Assignment - Light Yellow Background */}
                        <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
                            <div className="flex items-center mb-3">
                                <Users className="h-5 w-5 text-yellow-600 mr-2" />
                                <h2 className="text-lg font-semibold text-yellow-800">Status & Assignment</h2>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
                                <div>
                                    <label className="block text-sm font-medium text-yellow-700 mb-1">
                                        Job Status <span className="text-red-500">*</span>
                                    </label>
                                    <select
                                        name="jobStatus"
                                        required
                                        className="w-full px-3 py-2.5 border border-yellow-300 rounded-md focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                                        value={formData.jobStatus}
                                        onChange={handleInputChange}
                                    >
                                        <option value="">Select Job Status</option>
                                        {jobStatuses.map((status, index) => (
                                            <option key={index} value={status}>
                                                {status}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-yellow-700 mb-1">
                                        Recruiter <span className="text-red-500">*</span>
                                    </label>
                                    <SearchableDropdown
                                        label=""
                                        name="recruiter"
                                        value={formData.recruiter}
                                        onChange={(value) => setFormData({ ...formData, recruiter: value })}
                                        suggestions={allRecruiters}
                                        placeholder="Search for existing recruiter or enter new"
                                        required={true}
                                        isNewMode={false}
                                        onToggleNewMode={() => { }}
                                        newModeText=""
                                        selectExistingText=""
                                        addNewText=""
                                        simpleMode={true}
                                        borderColor="yellow"
                                    />
                                </div>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-yellow-700 mb-1">
                                    Detailed JD
                                </label>
                                <div className="flex items-center space-x-4">
                                    <label className="cursor-pointer bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors">
                                        <Upload className="h-4 w-4 inline mr-2" />
                                        Choose file(s)
                                        <input
                                            type="file"
                                            className="hidden"
                                            accept=".pdf,.doc,.docx"
                                            multiple
                                            onChange={handleFileChange}
                                        />
                                    </label>
                                    <span className="text-gray-500">
                                        {formData.detailedJD ? (
                                            Array.isArray(formData.detailedJD)
                                                ? `${formData.detailedJD.length} files selected`
                                                : formData.detailedJD.name
                                        ) : "No file chosen"}
                                    </span>
                                    {formData.detailedJD && (
                                        <button
                                            type="button"
                                            className="text-red-600 hover:text-red-800 text-sm"
                                            onClick={() => setFormData({ ...formData, detailedJD: null })}
                                        >
                                            Clear
                                        </button>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Section 5: Job Description - White Background */}
                        <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                            <div className="flex items-center mb-3">
                                <FileText className="h-5 w-5 text-gray-600 mr-2" />
                                <h2 className="text-lg font-semibold text-gray-800">Job Description</h2>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Detailed Job Description
                                </label>
                                <textarea
                                    name="jobDescription"
                                    rows={6}
                                    className="w-full px-3 py-2.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
                                    value={formData.jobDescription}
                                    onChange={handleInputChange}
                                    placeholder="Enter detailed job description here..."
                                />
                            </div>
                        </div>

                        {/* Submit Button */}
                        <div className="flex justify-end space-x-4">
                            <button
                                type="button"
                                className="px-6 py-3 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                disabled={isSubmitting}
                                className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {isSubmitting ? (
                                    <>
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline"></div>
                                        Posting...
                                    </>
                                ) : (
                                    "Post Job"
                                )}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
}
