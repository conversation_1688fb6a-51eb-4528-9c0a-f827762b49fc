import { useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { useUser } from '@/contexts/user-context';
import { fetchJobs } from '@/store/slices/jobsSlice';
import { fetchCandidates } from '@/store/slices/candidatesSlice';
import { fetchActiveUsers } from '@/store/slices/activeUsersSlice';
import { selectJobs, selectJobsLoading, selectJobsError } from '@/store/selectors/jobsSelectors';
import { selectCandidates, selectCandidatesLoading, selectCandidatesError } from '@/store/selectors/candidatesSelectors';
import { selectActiveUsers, selectActiveUsersLoading, selectActiveUsersError } from '@/store/selectors/activeUsersSelectors';

/**
 * Hook for optimized data management and access
 * Provides cached data with intelligent refresh strategies
 */
export const useDataManagement = () => {
    const dispatch = useAppDispatch();
    const { userRole, userName, userId } = useUser();

    // Get data from Redux store
    const jobs = useAppSelector(selectJobs);
    const jobsLoading = useAppSelector(selectJobsLoading);
    const jobsError = useAppSelector(selectJobsError);

    const candidates = useAppSelector(selectCandidates);
    const candidatesLoading = useAppSelector(selectCandidatesLoading);
    const candidatesError = useAppSelector(selectCandidatesError);

    const activeUsers = useAppSelector(selectActiveUsers);
    const activeUsersLoading = useAppSelector(selectActiveUsersLoading);
    const activeUsersError = useAppSelector(selectActiveUsersError);

    // Check if data is stale (older than 5 minutes)
    const isDataStale = useCallback((lastFetched: string | null) => {
        if (!lastFetched) return true;
        const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
        return (new Date().getTime() - new Date(lastFetched).getTime()) > CACHE_DURATION;
    }, []);

    // Refresh specific data when needed
    const refreshJobs = useCallback(async (force: boolean = false) => {
        if (!userName) return;

        try {
            await dispatch(fetchJobs({ username: userName, force })).unwrap();
        } catch (error) {
            // Failed to refresh jobs
        }
    }, [dispatch, userName]);

    const refreshCandidates = useCallback(async (force: boolean = false) => {
        if (!userId || !userName) return;

        try {
            await dispatch(fetchCandidates({
                userId,
                userType: userRole || 'recruiter',
                userName,
                force
            })).unwrap();
        } catch (error) {
            // Failed to refresh candidates
        }
    }, [dispatch, userId, userName, userRole]);

    const refreshActiveUsers = useCallback(async () => {
        if (userRole !== 'manager') return;

        try {
            await dispatch(fetchActiveUsers()).unwrap();
        } catch (error) {
            // Failed to refresh active users
        }
    }, [dispatch, userRole]);

    // Smart refresh - only refresh if data is stale or forced
    const smartRefresh = useCallback(async (dataType: 'jobs' | 'candidates' | 'activeUsers' | 'all', force: boolean = false) => {
        if (force) {
            // Force refresh all specified data
            switch (dataType) {
                case 'jobs':
                    await refreshJobs(true);
                    break;
                case 'candidates':
                    await refreshCandidates(true);
                    break;
                case 'activeUsers':
                    await refreshActiveUsers();
                    break;
                case 'all':
                    await Promise.all([
                        refreshJobs(true),
                        refreshCandidates(true),
                        refreshActiveUsers()
                    ]);
                    break;
            }
        } else {
            // Check if data needs refresh based on staleness
            // This would require adding lastFetched to your selectors
            // For now, we'll just refresh if no data exists
            switch (dataType) {
                case 'jobs':
                    if (jobs.length === 0) await refreshJobs();
                    break;
                case 'candidates':
                    if (candidates.length === 0) await refreshCandidates();
                    break;
                case 'activeUsers':
                    if (activeUsers.length === 0) await refreshActiveUsers();
                    break;
                case 'all':
                    await Promise.all([
                        jobs.length === 0 ? refreshJobs() : Promise.resolve(),
                        candidates.length === 0 ? refreshCandidates() : Promise.resolve(),
                        activeUsers.length === 0 ? refreshActiveUsers() : Promise.resolve()
                    ]);
                    break;
            }
        }
    }, [refreshJobs, refreshCandidates, refreshActiveUsers, jobs.length, candidates.length, activeUsers.length]);

    return {
        // Data
        jobs,
        candidates,
        activeUsers,

        // Loading states
        jobsLoading,
        candidatesLoading,
        activeUsersLoading,

        // Error states
        jobsError,
        candidatesError,
        activeUsersError,

        // Actions
        refreshJobs,
        refreshCandidates,
        refreshActiveUsers,
        smartRefresh,

        // Utilities
        isDataStale,
        userRole,
        userName,
        userId
    };
};
