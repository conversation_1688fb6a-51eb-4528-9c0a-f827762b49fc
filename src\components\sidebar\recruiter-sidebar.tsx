import { Link, useLocation } from "react-router-dom";
import { useLogoutConfirmation } from "@/hooks/use-logout-confirmation";
import { ProfileAvatar } from "@/components/ui/profile-avatar";
import {
  Calendar,
  Home,
  ClipboardList,
  UserPlus,
  FileText,
  BarChart2,
  UserCheck,
  Lock,
  LogOut,
  Users,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";

interface MenuItem {
  title: string;
  url: string;
  icon: React.ComponentType<{ className?: string }>;
}

interface MenuSection {
  title?: string;
  items: MenuItem[];
}

const menuSections: MenuSection[] = [
  {
    items: [{ title: "Dashboard", url: "/recruiter/dashboard", icon: Home }],
  },
  {
    title: "CANDIDATES",
    items: [
      {
        title: "Candidate Repository",
        url: "/recruiter/candidate",
        icon: Users,
      },
      {
        title: "Register Candidate",
        url: "/register-candidate",
        icon: UserPlus,
      },
      {
        title: "Peer Assigned Profiles",
        url: "/recruiter/peer-assigned-profiles",
        icon: FileText,
      },
    ],
  },
  {
    title: "RECRUITMENT",
    items: [
      {
        title: "Assigned Requirements",
        url: "/recruiter/requirements",
        icon: ClipboardList,
      },
    ],
  },
  {
    title: "GENERAL",
    items: [
      { title: "Analytics", url: "/recruiter/analytics", icon: BarChart2 },
      {
        title: "Profile Analysis",
        url: "/recruiter/profile-analysis",
        icon: UserCheck,
      },
      { title: "Calendar", url: "/calendar", icon: Calendar },
      { title: "Help and Support", url: "/help-and-support", icon: Users },
      { title: "Change Password", url: "/change-password", icon: Lock },
      { title: "Logout", url: "#", icon: LogOut },
    ],
  },
];

export function RecruiterSidebar() {
  const location = useLocation();
  const currentPath = location.pathname;
  const { LogoutDialog, handleLogoutClick } = useLogoutConfirmation();

  return (
    <Sidebar className="bg-[var(--sidebar)] text-[var(--sidebar-foreground)] w-52 border-r border-[var(--sidebar-border)]">
      {/* Profile Avatar Section */}
      <ProfileAvatar />

      <SidebarContent>
        {menuSections.map((section, sectionIndex) => (
          <SidebarGroup key={sectionIndex}>
            {section.title && (
              <SidebarGroupLabel className="text-xs font-semibold text-gray-400 uppercase tracking-wider px-1 py-0.5 ml-2">
                {section.title}
              </SidebarGroupLabel>
            )}
            <SidebarGroupContent>
              <SidebarMenu>
                {section.items.map((item, itemIndex) => (
                  <SidebarMenuItem key={itemIndex} className="ml-2">
                    {item.title === "Logout" ? (
                      <SidebarMenuButton
                        onClick={handleLogoutClick}
                        className="w-full px-1 py-0.5 cursor-pointer"
                      >
                        <LogOut className="h-4 w-4 sidebar-icon-system" />
                        <span>Logout</span>
                      </SidebarMenuButton>
                    ) : (
                      <SidebarMenuButton
                        asChild
                        isActive={currentPath === item.url}
                        className="w-full px-1 py-0.5"
                      >
                        <Link to={item.url}>
                          <item.icon className={`h-4 w-4 ${item.title === "Dashboard" ? "sidebar-icon-dashboard" :
                            item.title === "Candidate Repository" || item.title === "Register Candidate" || item.title === "Peer Assigned Profiles" ? "sidebar-icon-candidates" :
                              item.title === "Assigned Requirements" ? "sidebar-icon-recruitment" :
                                item.title === "Analytics" || item.title === "Calendar" || item.title === "Help and Support" ? "sidebar-icon-general" :
                                  item.title === "Profile Analysis" || item.title === "Change Password" ? "sidebar-icon-user" :
                                    ""
                            }`} />
                          <span>{item.title}</span>
                        </Link>
                      </SidebarMenuButton>
                    )}
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>

      {LogoutDialog()}
    </Sidebar>
  );
}
