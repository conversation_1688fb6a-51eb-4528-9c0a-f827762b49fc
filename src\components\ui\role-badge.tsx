import { User<PERSON><PERSON><PERSON>, <PERSON>, CheckCircle } from "lucide-react";
import { useUser } from "@/contexts/user-context";

interface RoleBadgeProps {
  className?: string;
  showTooltip?: boolean;
}

export function RoleBadge({
  className = "",
  showTooltip = true,
}: RoleBadgeProps) {
  const { userRole, peerStatus } = useUser();

  // Determine the role display based on peer_status and userRole
  const getRoleInfo = () => {
    if (peerStatus) {
      return {
        title: "Peer Reviewer",
        icon: CheckCircle,
        bgColor: "bg-purple-100 text-purple-900",
        textColor: "text-gray-800",
      };
    }

    if (userRole === "manager") {
      return {
        title: "Manager",
        icon: UserCheck,
        bgColor: "bg-green-600",
        textColor: "text-white",
      };
    }

    // Default to recruiter
    return {
      title: "Recruiter",
      icon: Users,
      bgColor: "bg-blue-500",
      textColor: "text-white",
    };
  };

  const roleInfo = getRoleInfo();
  const IconComponent = roleInfo.icon;

  return (
    <span
      title={showTooltip ? roleInfo.title : undefined}
      className={`inline-flex items-center justify-center h-5 w-5 ${roleInfo.bgColor} ${roleInfo.textColor} rounded-full ${className}`}
    >
      <IconComponent className="h-3 w-3" />
    </span>
  );
}

// Alternative text-based badge
export function RoleTextBadge({ className = "" }: { className?: string }) {
  const { userRole, peerStatus } = useUser();

  const getRoleInfo = () => {
    if (peerStatus) {
      return {
        title: "Peer Reviewer",
        bgColor: "bg-yellow-100",
        textColor: "text-yellow-800",
        borderColor: "border-yellow-300",
      };
    }

    if (userRole === "manager") {
      return {
        title: "Manager",
        bgColor: "bg-green-100",
        textColor: "text-green-800",
        borderColor: "border-green-300",
      };
    }

    return {
      title: "Recruiter",
      bgColor: "bg-blue-100",
      textColor: "text-blue-800",
      borderColor: "border-blue-300",
    };
  };

  const roleInfo = getRoleInfo();

  return (
    <span
      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${roleInfo.bgColor} ${roleInfo.textColor} border ${roleInfo.borderColor} ${className}`}
    >
      {roleInfo.title}
    </span>
  );
}
