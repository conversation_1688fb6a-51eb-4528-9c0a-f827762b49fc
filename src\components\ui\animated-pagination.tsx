import { motion } from "framer-motion";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface AnimatedPaginationProps {
  /**
   * Current page number
   */
  currentPage: number;

  /**
   * Total number of pages
   */
  totalPages: number;

  /**
   * Function to handle page change
   */
  onPageChange: (page: number) => void;

  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * A component that adds Framer Motion animations to pagination controls
 */
export function AnimatedPagination({
  currentPage,
  totalPages,
  onPageChange,
  className = "",
}: AnimatedPaginationProps) {
  return (
    <div className={`flex items-center gap-1 ${className}`}>
      <motion.button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        whileHover={{ backgroundColor: "#f3f4f6" }}
        whileTap={{ scale: 0.95 }}
        transition={{ duration: 0.2 }}
        className={`p-1.5 rounded-md ${
          currentPage === 1
            ? "text-gray-400 cursor-not-allowed"
            : "text-gray-700 hover:bg-gray-100"
        }`}
      >
        <motion.div whileHover={{ x: -2 }} transition={{ duration: 0.2 }}>
          <ChevronLeft className="h-4 w-4" />
        </motion.div>
      </motion.button>

      {/* Show limited page numbers with ellipsis for large page counts */}
      {Array.from({ length: totalPages }).map((_, index) => {
        const pageNumber = index + 1;

        // Always show first page, last page, current page, and pages around current
        if (
          pageNumber === 1 ||
          pageNumber === totalPages ||
          (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1)
        ) {
          return (
            <motion.button
              key={pageNumber}
              onClick={() => onPageChange(pageNumber)}
              whileHover={{
                y: -1,
              }}
              whileTap={{ scale: 0.95 }}
              animate={
                currentPage === pageNumber
                  ? { scale: [1, 1.05, 1] }
                  : { scale: 1 }
              }
              transition={{ duration: 0.2 }}
              className={`px-2.5 py-1 rounded-md text-xs transition-colors ${
                currentPage === pageNumber
                  ? "bg-blue-700 text-white hover:bg-blue-600"
                  : "text-gray-700 hover:bg-gray-100"
              }`}
            >
              {pageNumber}
            </motion.button>
          );
        }

        // Show ellipsis for breaks in sequence
        if (
          (pageNumber === 2 && currentPage > 3) ||
          (pageNumber === totalPages - 1 && currentPage < totalPages - 2)
        ) {
          return (
            <span key={pageNumber} className="px-1">
              ...
            </span>
          );
        }

        // Hide other pages
        return null;
      })}

      <motion.button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        whileHover={{ backgroundColor: "#f3f4f6" }}
        whileTap={{ scale: 0.95 }}
        transition={{ duration: 0.2 }}
        className={`p-1.5 rounded-md ${
          currentPage === totalPages
            ? "text-gray-400 cursor-not-allowed"
            : "text-gray-700 hover:bg-gray-100"
        }`}
      >
        <motion.div whileHover={{ x: 2 }} transition={{ duration: 0.2 }}>
          <ChevronRight className="h-4 w-4" />
        </motion.div>
      </motion.button>
    </div>
  );
}
