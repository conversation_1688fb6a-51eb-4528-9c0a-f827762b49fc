import { useState, useEffect } from "react";
import { 
  AnimatedTable, 
  AnimatedTableRow, 
  Animated<PERSON><PERSON><PERSON><PERSON>, 
  AnimatedTable<PERSON>eader,
  AnimatedSortIcon,
  AnimatedPagination
} from "@/components/ui/animated-table";
import { useTableSort } from "@/hooks/use-table-sort";
import { useTablePagination } from "@/hooks/use-table-pagination";
import { Button } from "@/components/ui/button";

// Sample data type
interface Employee {
  id: number;
  name: string;
  email: string;
  role: string;
  department: string;
  status: string;
  joinDate: string;
}

// Sample data
const generateEmployees = (count: number): Employee[] => {
  return Array.from({ length: count }, (_, i) => ({
    id: i + 1,
    name: ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"][
      Math.floor(Math.random() * 5)
    ],
    email: `employee${i + 1}@example.com`,
    role: ["<PERSON><PERSON><PERSON>", "Designer", "Manager", "HR", "QA"][
      Math.floor(Math.random() * 5)
    ],
    department: ["Engineering", "Design", "Management", "HR", "QA"][
      Math.floor(Math.random() * 5)
    ],
    status: ["Active", "On Leave", "Terminated", "Probation"][
      Math.floor(Math.random() * 4)
    ],
    joinDate: new Date(
      2020 + Math.floor(Math.random() * 3),
      Math.floor(Math.random() * 12),
      Math.floor(Math.random() * 28) + 1
    ).toLocaleDateString(),
  }));
};

export function AnimatedTableExample() {
  const [isLoading, setIsLoading] = useState(true);
  const [employees, setEmployees] = useState<Employee[]>([]);
  
  // Initialize sorting
  const { 
    sortField, 
    sortDirection, 
    handleSort, 
    sortData 
  } = useTableSort<Employee>({
    initialSortField: 'id',
    initialSortDirection: 'asc'
  });
  
  // Initialize pagination
  const { 
    currentPage, 
    itemsPerPage, 
    tableControls,
    handlePageChange, 
    handleItemsPerPageChange,
    paginateData,
    getTotalPages
  } = useTablePagination({
    initialItemsPerPage: 5
  });
  
  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setEmployees(generateEmployees(25));
      setIsLoading(false);
    };
    
    loadData();
  }, []);
  
  // Sort and paginate data
  const sortedData = sortData(employees);
  const paginatedData = paginateData(sortedData);
  const totalPages = getTotalPages(sortedData.length);
  
  // Refresh data with animation
  const refreshData = async () => {
    setIsLoading(true);
    
    // Animate table out
    await tableControls.start({
      opacity: 0,
      y: 20,
      transition: { duration: 0.3 }
    });
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // Generate new data
    const newData = generateEmployees(Math.floor(Math.random() * 15) + 15);
    setEmployees(newData);
    
    setIsLoading(false);
    
    // Animate table back in
    tableControls.start({
      opacity: 1,
      y: 0,
      transition: { duration: 0.5, type: "spring", stiffness: 100 }
    });
  };
  
  // Table headers
  const headers = (
    <tr>
      {[
        { field: 'id', label: 'ID' },
        { field: 'name', label: 'Name' },
        { field: 'email', label: 'Email' },
        { field: 'role', label: 'Role' },
        { field: 'department', label: 'Department' },
        { field: 'status', label: 'Status' },
        { field: 'joinDate', label: 'Join Date' },
      ].map((column) => (
        <AnimatedTableHeader
          key={column.field}
          onClick={() => handleSort(column.field as keyof Employee)}
        >
          <div className="flex items-center space-x-1">
            <span>{column.label}</span>
            <AnimatedSortIcon
              direction={sortField === column.field ? sortDirection : null}
              active={sortField === column.field}
            />
          </div>
        </AnimatedTableHeader>
      ))}
      <AnimatedTableHeader>Actions</AnimatedTableHeader>
    </tr>
  );
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">Employees</h2>
        <Button onClick={refreshData} disabled={isLoading}>
          {isLoading ? "Refreshing..." : "Refresh Data"}
        </Button>
      </div>
      
      <AnimatedTable
        headers={headers}
        isLoading={isLoading}
        className="border-gray-200"
      >
        {paginatedData.map((employee, index) => (
          <AnimatedTableRow key={employee.id} index={index}>
            <AnimatedTableCell>{employee.id}</AnimatedTableCell>
            <AnimatedTableCell className="font-medium">{employee.name}</AnimatedTableCell>
            <AnimatedTableCell>{employee.email}</AnimatedTableCell>
            <AnimatedTableCell>{employee.role}</AnimatedTableCell>
            <AnimatedTableCell>{employee.department}</AnimatedTableCell>
            <AnimatedTableCell>
              <span
                className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                  employee.status === "Active"
                    ? "bg-green-100 text-green-800"
                    : employee.status === "On Leave"
                    ? "bg-yellow-100 text-yellow-800"
                    : employee.status === "Terminated"
                    ? "bg-red-100 text-red-800"
                    : "bg-blue-100 text-blue-800"
                }`}
              >
                {employee.status}
              </span>
            </AnimatedTableCell>
            <AnimatedTableCell>{employee.joinDate}</AnimatedTableCell>
            <AnimatedTableCell>
              <div className="flex space-x-2">
                <button className="text-blue-600 hover:text-blue-900">View</button>
                <button className="text-green-600 hover:text-green-900">Edit</button>
              </div>
            </AnimatedTableCell>
          </AnimatedTableRow>
        ))}
      </AnimatedTable>
      
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-700">Rows per page:</span>
          <select
            value={itemsPerPage}
            onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
            className="rounded border border-gray-300 py-1 px-2 text-sm"
          >
            {[5, 10, 20, 50].map((size) => (
              <option key={size} value={size}>
                {size}
              </option>
            ))}
          </select>
        </div>
        
        <AnimatedPagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
        />
      </div>
    </div>
  );
}
