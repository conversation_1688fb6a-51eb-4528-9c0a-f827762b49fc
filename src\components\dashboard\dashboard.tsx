import { useUser } from "@/contexts/user-context";
import { useDataPrefetch } from "@/hooks/use-data-prefetch";
import { ManagerDashboard } from "./manager-dashboard";
import { RecruiterDashboard } from "./recruiter-dashboard";

export function Dashboard() {
  const { userRole } = useUser();
  
  // Initialize data pre-fetching
  useDataPrefetch();

  if (userRole === "manager") {
    return <ManagerDashboard />;
  } else {
    return <RecruiterDashboard />;
  }
}
