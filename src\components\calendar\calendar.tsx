import React, { useState, useMemo, useRef, useEffect } from "react";
import { ChevronLeft, ChevronRight, Paperclip, X, ExternalLink, Calendar, Clock, Users, FileText, RefreshCw } from "lucide-react";
import { ThreeDots } from "react-loader-spinner";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { ScheduleMeetingModal } from "@/components/modals/schedule-meeting-modal";
import { InPersonMeetingModal } from "@/components/modals/InPersonMeetingModal";
import { CallMeetingModal } from "@/components/modals/CallMeetingModal";
import { ApiService, type MeetingData, type CallEventData, type ZoomMeetingData } from "@/services/api";
import { type Candidate } from "@/types/candidate";

// Extended candidate interface for calendar with selected date/time
interface CalendarCandidate extends Candidate {
  selectedDate?: Date;
  selectedStartTime?: string;
  selectedEndTime?: string;
}

// Mockup for platform logo images
const teamsLogoPath = "https://img.icons8.com/color/48/microsoft-teams-2019.png";
const zoomLogoPath = "https://img.icons8.com/color/48/zoom.png";
const callLogoPath = "https://img.icons8.com/ios-filled/50/phone.png";

// --- INTERFACES & CONSTANTS ---

interface Meeting {
  id?: string;
  date: string;
  startTime: string;
  endTime: string;
  title: string;
  attendees?: string[];
  attendeesOptional?: string[];
  description?: string;
  timezone?: string;
  platform?: MeetingPlatform;
  files?: File[];
  joinUrl?: string;
  meetingData?: MeetingData;
}

interface Timezone {
  value: string;
  label: string;
}

type MeetingPlatform = "teams" | "zoom" | "call" | "inperson" | "";

const TIME_SLOT_START_HOUR = 9;
const TIME_SLOT_END_HOUR = 17;
const TIME_SLOT_INTERVAL = 30;
const MONTH_NAMES = [
  "January", "February", "March", "April", "May", "June",
  "July", "August", "September", "October", "November", "December"
];

// Available emails for attendee selection
const allAvailableEmails = [
  "<EMAIL>", "<EMAIL>", "<EMAIL>",
  "<EMAIL>", "<EMAIL>", "<EMAIL>"
];

// --- HELPER FUNCTIONS ---

const formatDateToYYYYMMDD = (date: Date): string => {
  return date.toLocaleDateString("en-CA"); // YYYY-MM-DD format
};

const getMeetingBorderColor = (platform: MeetingPlatform): string => {
  switch (platform) {
    case 'teams':
      return 'border-l-4 border-blue-500';
    case 'zoom':
      return 'border-l-4 border-purple-500';
    case 'inperson':
      return 'border-l-4 border-orange-500';
    case 'call':
      return 'border-l-4 border-green-500';
    default:
      return 'border-l-4 border-gray-400';
  }
};

const getMeetingBackgroundColor = (platform: MeetingPlatform): string => {
  switch (platform) {
    case 'teams':
      return 'bg-blue-50/50';
    case 'zoom':
      return 'bg-purple-50/50';
    case 'inperson':
      return 'bg-orange-50/50';
    case 'call':
      return 'bg-green-50/50';
    default:
      return 'bg-gray-50/50';
  }
};

const timeToMinutes = (timeStr?: string): number => {
  if (!timeStr) return 0;
  const [hours, minutes] = timeStr.split(':').map(Number);
  return hours * 60 + minutes;
};

const fileToBase64 = (file: File): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const result = reader.result as string;
      const base64 = result.split(",")[1];
      resolve(base64);
    };
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });

const getTimezones = (): Timezone[] => [
  { value: "Asia/Kolkata", label: "(GMT+05:30) India Standard Time" },
  { value: "America/New_York", label: "(GMT-05:00) Eastern Time (US & Canada)" },
  { value: "Europe/London", label: "(GMT+00:00) Greenwich Mean Time" },
  { value: "Australia/Sydney", label: "(GMT+10:00) Sydney" },
  { value: "Asia/Tokyo", label: "(GMT+09:00) Tokyo" },
];


const MergedCalendarView: React.FC = () => {
  // --- STATE MANAGEMENT ---
  const [currentMonthDate, setCurrentMonthDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [scheduledMeetings, setScheduledMeetings] = useState<Meeting[]>([]);

  // Form State
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [selectedStartTime, setSelectedStartTime] = useState("");
  const [selectedEndTime, setSelectedEndTime] = useState("");
  const [timezone, setTimezone] = useState("Asia/Kolkata");
  const [meetingPlatform, setMeetingPlatform] = useState<MeetingPlatform>("");

  // Attendee State
  const [attendees, setAttendees] = useState<string[]>([]);
  const [attendeesOptional, setAttendeesOptional] = useState<string[]>([]);
  const [attendeeSearch, setAttendeeSearch] = useState("");
  const [optionalAttendeeSearch, setOptionalAttendeeSearch] = useState("");

  // File Attachment State
  const [files, setFiles] = useState<File[]>([]);

  // UI Control State
  const [showForm, setShowForm] = useState(false);
  const [waitForSubmission, setWaitForSubmission] = useState(false);

  // Backend meetings state
  const [backendMeetings, setBackendMeetings] = useState<MeetingData[]>([]);
  const [loadingMeetings, setLoadingMeetings] = useState(false);

  // Call events state
  const [callEvents, setCallEvents] = useState<CallEventData[]>([]);
  const [loadingCallEvents, setLoadingCallEvents] = useState(false);

  // Zoom meetings state
  const [zoomMeetings, setZoomMeetings] = useState<ZoomMeetingData[]>([]);
  const [loadingZoomMeetings, setLoadingZoomMeetings] = useState(false);

  // Combined loading state for all meeting types (for future use)
  // const isLoadingAnyMeetings = loadingMeetings || loadingCallEvents || loadingZoomMeetings;

  // Schedule meeting modal state
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [showInPersonModal, setShowInPersonModal] = useState(false);
  const [showCallModal, setShowCallModal] = useState(false);
  const [selectedCandidate, setSelectedCandidate] = useState<CalendarCandidate | null>(null);
  const [selectedMeetingType, setSelectedMeetingType] = useState<"Teams" | "Zoom" | undefined>(undefined);

  const attendeeInputRef = useRef<HTMLInputElement>(null);
  const optionalAttendeeInputRef = useRef<HTMLInputElement>(null);

  // --- MEMOIZED VALUES & EFFECTS ---

  const today = useMemo(() => {
    const now = new Date();
    now.setHours(0, 0, 0, 0);
    return now;
  }, []);

  const timezones = useMemo(() => getTimezones(), []);

  // Fetch meetings from backend
  useEffect(() => {
    console.log('📅 Calendar component mounted, starting fetchAllMeetingsData...');
    const fetchAllMeetingsData = async () => {
      console.log('🔧 fetchAllMeetingsData function called');
      const userId = localStorage.getItem('userId') || ''; // Default fallback
      console.log('👤 User ID from localStorage:', userId);

      // Fetch Teams/Outlook meetings
      const fetchMeetings = async () => {
        console.log('🔄 Starting to fetch Teams/Outlook meetings...');
        try {
          setLoadingMeetings(true);
          const response = await ApiService.fetchAllMeetings(userId);
          console.log('✅ Teams/Outlook meetings fetched successfully:', response);
          // Filter out meetings with missing required fields
          const validMeetings = (response.meetings || []).filter(meeting =>
            meeting &&
            meeting.meeting_id &&
            meeting.start_date &&
            meeting.start_time &&
            meeting.end_time &&
            meeting.subject
          );
          setBackendMeetings(validMeetings);
          console.log(`📊 Teams meetings: ${validMeetings.length} valid meetings found`);
        } catch (error) {
          console.error('❌ Failed to fetch meetings:', error);
          toast.error('Failed to fetch meetings');
        } finally {
          setLoadingMeetings(false);
        }
      };

      // Fetch call events
      const fetchCallEvents = async () => {
        console.log('🔄 Starting to fetch call events...');
        try {
          setLoadingCallEvents(true);
          const response = await ApiService.fetchAllCallEvents(userId);
          console.log('✅ Call events fetched successfully:', response);
          // Handle direct array response or wrapped response
          const callEventsArray = Array.isArray(response) ? response : (response.call_events || []);
          // Filter out call events with missing required fields
          const validCallEvents = callEventsArray.filter(event =>
            event &&
            (event.id || event.event_id) &&
            event.date &&
            event.time &&
            event.name &&
            event.purpose
          );
          setCallEvents(validCallEvents);
          console.log(`📊 Call events: ${validCallEvents.length} valid events found`);
        } catch (error) {
          console.error('❌ Failed to fetch call events:', error);
          toast.error('Failed to fetch call events');
        } finally {
          setLoadingCallEvents(false);
        }
      };

      // Fetch zoom meetings
      const fetchZoomMeetings = async () => {
        console.log('🔄 Starting to fetch Zoom meetings...');
        try {
          setLoadingZoomMeetings(true);
          const response = await ApiService.fetchZoomMeetings();
          console.log('✅ Zoom meetings fetched successfully:', response);
          // Handle different response formats
          const zoomMeetingsArray = response.meetings || response.zoom_meetings || [];
          // Filter out zoom meetings with missing required fields
          const validZoomMeetings = zoomMeetingsArray.filter(meeting =>
            meeting &&
            (meeting.meeting_id || meeting.event_id || meeting.id) &&
            meeting.start_date &&
            meeting.start_time &&
            meeting.end_time &&
            meeting.subject
          );
          setZoomMeetings(validZoomMeetings);
          console.log(`📊 Zoom meetings: ${validZoomMeetings.length} valid meetings found`);
        } catch (error) {
          console.error('❌ Failed to fetch zoom meetings:', error);
          toast.error('Failed to fetch zoom meetings');
        } finally {
          setLoadingZoomMeetings(false);
        }
      };

      // Fetch all meeting types in parallel
      console.log('🚀 Starting parallel API calls for all meeting types...');
      await Promise.all([
        fetchMeetings(),
        fetchCallEvents(),
        fetchZoomMeetings()
      ]);
      console.log('🎉 All API calls completed!');
    };

    console.log('🚀 Calling fetchAllMeetingsData...');
    fetchAllMeetingsData();
  }, []);

  const currentYear = currentMonthDate.getFullYear();
  const currentMonth = currentMonthDate.getMonth();

  const daysInMonth = useMemo(() => new Date(currentYear, currentMonth + 1, 0).getDate(), [currentYear, currentMonth]);
  const firstDayOfMonth = useMemo(() => new Date(currentYear, currentMonth, 1).getDay(), [currentYear, currentMonth]);

  const meetingsByDate = useMemo(() => {
    const map = new Map<string, Meeting[]>();
    scheduledMeetings.forEach((meeting) => {
      const list = map.get(meeting.date) || [];
      list.push(meeting);
      list.sort((a, b) => timeToMinutes(a.startTime) - timeToMinutes(b.startTime));
      map.set(meeting.date, list);
    });

    // Add backend meetings (Teams/Outlook)
    try {
      backendMeetings.forEach((backendMeeting) => {
        // Skip invalid meetings
        if (!backendMeeting || !backendMeeting.start_date || !backendMeeting.start_time || !backendMeeting.end_time || !backendMeeting.subject) {
          console.warn('Skipping invalid meeting:', backendMeeting);
          return;
        }

        const date = backendMeeting.start_date;
        const list = map.get(date) || [];

        // Convert backend meeting to local meeting format
        const localMeeting: Meeting = {
          id: backendMeeting.meeting_id || `backend-${Date.now()}`,
          date: backendMeeting.start_date,
          startTime: backendMeeting.start_time,
          endTime: backendMeeting.end_time,
          title: backendMeeting.subject,
          attendees: backendMeeting.attendees ? backendMeeting.attendees.split(',') : [],
          attendeesOptional: backendMeeting.cc_recipients ? backendMeeting.cc_recipients.split(',') : [],
          description: backendMeeting.description || '',
          timezone: backendMeeting.time_zone || 'UTC',
          platform: (backendMeeting.meeting_type?.toLowerCase() as MeetingPlatform) || '',
          joinUrl: backendMeeting.join_url || '',
          meetingData: backendMeeting
        };

        console.log('Converted backend meeting:', localMeeting);
        console.log('Original join_url:', backendMeeting.join_url);

        list.push(localMeeting);
        map.set(date, list);
      });
    } catch (error) {
      console.error('Error processing backend meetings:', error);
      // Continue with local meetings only if backend processing fails
    }

    // Add call events
    try {
      callEvents.forEach((callEvent) => {
        // Skip invalid call events
        if (!callEvent || !callEvent.date || !callEvent.time || !callEvent.name || !callEvent.purpose) {
          console.warn('Skipping invalid call event:', callEvent);
          return;
        }

        const date = callEvent.date;
        const list = map.get(date) || [];

        // Calculate end time (add 30 minutes to start time for call events)
        const startTime = callEvent.time;
        const [hours, minutes] = startTime.split(':').map(Number);
        const startMinutes = hours * 60 + minutes;
        const endMinutes = startMinutes + 30; // 30 minute default duration
        const endHours = Math.floor(endMinutes / 60);
        const endMins = endMinutes % 60;
        const endTime = `${endHours.toString().padStart(2, '0')}:${endMins.toString().padStart(2, '0')}`;

        // Determine meeting platform based on meeting_type
        let platform: MeetingPlatform = 'call';
        if (callEvent.meeting_type) {
          switch (callEvent.meeting_type.toLowerCase()) {
            case 'call':
              platform = 'call';
              break;
            case 'in-person':
              platform = 'inperson';
              break;
            default:
              platform = 'call';
          }
        }

        // Convert call event to local meeting format
        const localMeeting: Meeting = {
          id: (callEvent.event_id || callEvent.id || `call-${Date.now()}`).toString(),
          date: callEvent.date,
          startTime: startTime,
          endTime: endTime,
          title: `${callEvent.meeting_type || 'Call'}: ${callEvent.name} - ${callEvent.purpose}`,
          attendees: [callEvent.name],
          attendeesOptional: [],
          description: `${callEvent.meeting_type || 'Call'} meeting with ${callEvent.name} (${callEvent.mobile}) - Purpose: ${callEvent.purpose}${callEvent.location ? ` - Location: ${callEvent.location}` : ''}`,
          timezone: 'UTC',
          platform: platform,
          joinUrl: platform === 'call' ? `tel:${callEvent.mobile}` : '',
          meetingData: undefined
        };

        console.log('Converted call event:', localMeeting);

        list.push(localMeeting);
        map.set(date, list);
      });
    } catch (error) {
      console.error('Error processing call events:', error);
      // Continue with other meetings if call events processing fails
    }

    // Add zoom meetings
    try {
      zoomMeetings.forEach((zoomMeeting) => {
        // Skip invalid zoom meetings  
        if (!zoomMeeting || !zoomMeeting.start_date || !zoomMeeting.start_time || !zoomMeeting.end_time || !zoomMeeting.subject) {
          console.warn('Skipping invalid zoom meeting:', zoomMeeting);
          return;
        }

        const date = zoomMeeting.start_date;
        const list = map.get(date) || [];

        // Convert zoom meeting to local meeting format
        const localMeeting: Meeting = {
          id: (zoomMeeting.meeting_id || zoomMeeting.event_id || zoomMeeting.id || `zoom-${Date.now()}`).toString(),
          date: zoomMeeting.start_date,
          startTime: zoomMeeting.start_time,
          endTime: zoomMeeting.end_time,
          title: zoomMeeting.subject,
          attendees: Array.isArray(zoomMeeting.attendees) ? zoomMeeting.attendees : (zoomMeeting.attendees ? zoomMeeting.attendees.split(',') : []),
          attendeesOptional: Array.isArray(zoomMeeting.cc_recipients) ? zoomMeeting.cc_recipients : (zoomMeeting.cc_recipients ? zoomMeeting.cc_recipients.split(',') : []),
          description: zoomMeeting.description || '',
          timezone: zoomMeeting.timezone || zoomMeeting.time_zone || 'UTC',
          platform: 'zoom' as MeetingPlatform,
          joinUrl: zoomMeeting.join_url || '',
          meetingData: undefined
        };

        console.log('Converted zoom meeting:', localMeeting);

        list.push(localMeeting);
        map.set(date, list);
      });
    } catch (error) {
      console.error('Error processing zoom meetings:', error);
      // Continue with other meetings if zoom meetings processing fails
    }

    // Debug: Log the meetings map
    console.log('Meetings by date:', Array.from(map.entries()));
    console.log('Backend meetings count:', backendMeetings.length);
    console.log('Call events count:', callEvents.length);
    console.log('Zoom meetings count:', zoomMeetings.length);
    console.log('Scheduled meetings count:', scheduledMeetings.length);

    // Add a test meeting for debugging if no meetings exist
    if (map.size === 0) {
      const today = new Date();
      const todayString = today.toISOString().split('T')[0];
      console.log('No meetings found, adding test meeting for:', todayString);

      const testMeeting: Meeting = {
        id: 'debug-1',
        date: todayString,
        startTime: '10:00',
        endTime: '10:30',
        title: 'Debug Meeting',
        platform: 'teams',
        joinUrl: '',
        meetingData: undefined
      };

      map.set(todayString, [testMeeting]);
    }

    // Sort meetings by start time
    map.forEach((meetings) => {
      meetings.sort((a, b) => timeToMinutes(a.startTime) - timeToMinutes(b.startTime));
    });

    return map;
  }, [scheduledMeetings, backendMeetings, callEvents, zoomMeetings]);

  // --- TIME SLOT AVAILABILITY LOGIC ---

  const availableStartTimes = useMemo((): string[] => {
    if (!selectedDate) return [];
    const now = new Date("2025-08-14T15:14:15"); // Hardcoded for demo
    const selectedDateString = formatDateToYYYYMMDD(selectedDate);
    const meetingsOnSelectedDate = meetingsByDate.get(selectedDateString) || [];
    const blockedSlots = new Set<number>();
    meetingsOnSelectedDate.forEach((meeting) => {
      const startMinutes = timeToMinutes(meeting.startTime);
      const endMinutes = timeToMinutes(meeting.endTime);
      for (let m = startMinutes; m < endMinutes; m += TIME_SLOT_INTERVAL) {
        blockedSlots.add(m);
      }
    });

    const options: string[] = [];
    const tempDate = new Date(selectedDate);
    tempDate.setSeconds(0, 0);

    for (let hour = TIME_SLOT_START_HOUR; hour < TIME_SLOT_END_HOUR; hour++) {
      for (let minute = 0; minute < 60; minute += TIME_SLOT_INTERVAL) {
        tempDate.setHours(hour, minute);
        const currentSlotMinutes = hour * 60 + minute;
        if (selectedDate.getTime() === today.getTime() && tempDate < now) continue;
        if (blockedSlots.has(currentSlotMinutes)) continue;
        options.push(tempDate.toLocaleTimeString("en-US", { hour: "numeric", minute: "2-digit", hour12: true }));
      }
    }
    return options;
  }, [selectedDate, today, meetingsByDate]);

  const availableEndTimes = useMemo((): string[] => {
    if (!selectedDate || !selectedStartTime) return [];
    const startMinutes = timeToMinutes(selectedStartTime);
    const selectedDateString = formatDateToYYYYMMDD(selectedDate);
    const meetingsOnSelectedDate = meetingsByDate.get(selectedDateString) || [];
    let nextMeetingStartMinutes = Infinity;
    meetingsOnSelectedDate.forEach((meeting) => {
      const meetingStart = timeToMinutes(meeting.startTime);
      if (meetingStart > startMinutes && meetingStart < nextMeetingStartMinutes) {
        nextMeetingStartMinutes = meetingStart;
      }
    });

    const options: string[] = [];
    const tempDate = new Date(selectedDate);
    tempDate.setSeconds(0, 0);
    const endHourLimit = TIME_SLOT_END_HOUR;

    for (let hour = TIME_SLOT_START_HOUR; hour <= endHourLimit; hour++) {
      for (let minute = 0; minute < 60; minute += TIME_SLOT_INTERVAL) {
        const currentSlotMinutes = hour * 60 + minute;
        if (hour === endHourLimit && minute > 0) continue;
        if (currentSlotMinutes <= startMinutes) continue;
        if (currentSlotMinutes > nextMeetingStartMinutes) continue;

        tempDate.setHours(hour, minute);
        options.push(tempDate.toLocaleTimeString("en-US", { hour: "numeric", minute: "2-digit", hour12: true }));
      }
    }
    return options;
  }, [selectedDate, selectedStartTime, meetingsByDate]);

  // --- EVENT HANDLERS ---

  const resetFormState = () => {
    setTitle("");
    setDescription("");
    setAttendees([]);
    setAttendeesOptional([]);
    setFiles([]);
    setSelectedStartTime("");
    setSelectedEndTime("");
    setMeetingPlatform("");
    setShowForm(false);
  };

  const handlePrevMonth = () => setCurrentMonthDate((prev) => new Date(prev.getFullYear(), prev.getMonth() - 1, 1));
  const handleNextMonth = () => setCurrentMonthDate((prev) => new Date(prev.getFullYear(), prev.getMonth() + 1, 1));

  const handleDateClick = (day: number) => {
    const clickedDate = new Date(currentYear, currentMonth, day);
    clickedDate.setHours(0, 0, 0, 0);
    console.log('Date clicked:', clickedDate.toISOString());
    console.log('Setting selected date to:', clickedDate);
    setSelectedDate(clickedDate);
    resetFormState();
  };

  const handlePlatformSelect = (platform: MeetingPlatform) => {
    if (platform === 'teams' || platform === 'zoom') {
      // Create a mock candidate for the schedule meeting modal
      const mockCandidate: CalendarCandidate = {
        id: 1,
        jobId: '1',
        firstName: 'Calendar',
        lastName: 'Meeting',
        email: localStorage.getItem('userEmail') || '',
        phone: '',
        client: 'Internal',
        profile: 'Meeting',
        skills: '',
        status: 'Active',
        appliedDate: new Date().toISOString(),
        source: 'Calendar',
        experience: 0,
        education: '',
        location: '',
        salary: '',
        notes: '',
        lastUpdated: new Date().toISOString(),
        comment: '',
        peerReviewer: '',
        recruiter: '',
        management: null
      };

      // Add selected date and time information to the candidate
      mockCandidate.selectedDate = selectedDate;
      mockCandidate.selectedStartTime = '';
      mockCandidate.selectedEndTime = '';

      // Set the meeting type based on platform selection
      const meetingType = platform === 'teams' ? 'Teams' : 'Zoom';
      console.log('Calendar: Setting meeting type to:', meetingType);
      setSelectedMeetingType(meetingType);
      setSelectedCandidate(mockCandidate);
      setShowScheduleModal(true);
    } else if (platform === 'inperson') {
      // Create a mock candidate for the in-person meeting modal
      const mockCandidate: CalendarCandidate = {
        id: 1,
        jobId: '1',
        firstName: 'Calendar',
        lastName: 'Meeting',
        email: localStorage.getItem('userEmail') || '',
        phone: '',
        client: 'Internal',
        profile: 'Meeting',
        skills: '',
        status: 'Active',
        appliedDate: new Date().toISOString(),
        source: 'Calendar',
        experience: 0,
        education: '',
        location: '',
        salary: '',
        notes: '',
        lastUpdated: new Date().toISOString(),
        comment: '',
        peerReviewer: '',
        recruiter: '',
        management: null
      };

      // Add selected date and time information to the candidate
      mockCandidate.selectedDate = selectedDate;
      mockCandidate.selectedStartTime = '';
      mockCandidate.selectedEndTime = '';

      setSelectedCandidate(mockCandidate);
      setShowInPersonModal(true);
    } else if (platform === 'call') {
      // Create a mock candidate for the call meeting modal
      const mockCandidate: CalendarCandidate = {
        id: 1,
        jobId: '1',
        firstName: 'Calendar',
        lastName: 'Meeting',
        email: localStorage.getItem('userEmail') || '',
        phone: '',
        client: 'Internal',
        profile: 'Meeting',
        skills: '',
        status: 'Active',
        appliedDate: new Date().toISOString(),
        source: 'Calendar',
        experience: 0,
        education: '',
        location: '',
        salary: '',
        notes: '',
        lastUpdated: new Date().toISOString(),
        comment: '',
        peerReviewer: '',
        recruiter: '',
        management: null
      };

      // Add selected date and time information to the candidate
      mockCandidate.selectedDate = selectedDate;
      mockCandidate.selectedStartTime = '';
      mockCandidate.selectedEndTime = '';

      setSelectedCandidate(mockCandidate);
      setShowCallModal(true);
    } else {
      setMeetingPlatform(platform);
      setShowForm(true);
    }
  };

  const handleJoinMeeting = (joinUrl: string) => {
    console.log('handleJoinMeeting called with URL:', joinUrl);
    if (joinUrl && joinUrl.trim() !== '') {
      try {
        console.log('Opening URL in new tab:', joinUrl);
        window.open(joinUrl, '_blank');
        toast.success('Opening meeting in new tab...');
      } catch (error) {
        console.error('Error opening URL:', error);
        toast.error('Failed to open meeting URL');
      }
    } else {
      console.error('Invalid join URL:', joinUrl);
      toast.error('No join URL available for this meeting');
    }
  };

  const handleBackToPlatformSelection = () => {
    setShowForm(false);
    setMeetingPlatform("");
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setFiles((prev) => [...prev, ...Array.from(e.target.files!)]);
    }
  };

  const handleRemoveFile = (indexToRemove: number) => {
    setFiles((prev) => prev.filter((_, index) => index !== indexToRemove));
  };

  const handleAttendeeAction = (email: string, type: 'required' | 'optional', action: 'add' | 'remove') => {
    const emailLower = email.trim().toLowerCase();
    if (!emailLower || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailLower)) {
      if (action === 'add') toast.warn("Invalid email format.");
      return;
    }

    const updater = type === 'required' ? setAttendees : setAttendeesOptional;
    const searchUpdater = type === 'required' ? setAttendeeSearch : setOptionalAttendeeSearch;

    updater(prev => {
      const exists = prev.includes(emailLower);
      if (action === 'add') {
        return exists ? prev : [...prev, emailLower];
      } else { // remove
        return prev.filter(e => e !== emailLower);
      }
    });

    if (action === 'add') {
      searchUpdater('');
    }
  };

  const handleSaveMeeting = async () => {
    if (!selectedDate || !selectedStartTime || !selectedEndTime || !timezone || !title || !meetingPlatform || attendees.length === 0) {
      toast.error("Please fill all required fields: Title, Attendees, Date, Time, and Timezone.");
      return;
    }
    setWaitForSubmission(true);

    // Simulate API payload creation
    await Promise.all(
      files.map(async (file) => ({
        file_name: file.name,
        file_content_base64: await fileToBase64(file),
      }))
    );

    const newMeeting: Meeting = {
      id: `m${Date.now()}`,
      date: formatDateToYYYYMMDD(selectedDate),
      startTime: selectedStartTime,
      endTime: selectedEndTime,
      title: title,
      attendees: attendees,
      attendeesOptional: attendeesOptional,
      description: description,
      timezone: timezone,
      platform: meetingPlatform,
      // files are handled separately for the payload
    };

    // Simulate API call
    setTimeout(() => {
      setScheduledMeetings((prev) => [...prev, newMeeting]);
      toast.success("Meeting scheduled successfully!");
      setWaitForSubmission(false);
      setSelectedDate(new Date());
      resetFormState();
    }, 1500);
  };

  const handleClose = () => {
    setSelectedDate(new Date());
    resetFormState();
  }

  // --- RENDER FUNCTIONS ---

  // Organization holidays - replace with your actual holiday calendar
  const organizationHolidays: string[] = [
    // Add your organization's holidays here in YYYY-MM-DD format
    // Example: '2025-01-01', '2025-12-25'
  ];

  const renderCalendarDays = () => {
    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(
        <div key={`empty-${i}`} className="w-full h-20 bg-transparent"></div>
      );
    }

    // Add the actual days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const cellDate = new Date(currentYear, currentMonth, day);
      cellDate.setHours(0, 0, 0, 0);
      const cellDateString = formatDateToYYYYMMDD(cellDate);
      const dayOfWeek = cellDate.getDay(); // 0 = Sunday, 6 = Saturday
      const isToday = cellDate.getTime() === today.getTime();
      const isSelected = selectedDate?.getTime() === cellDate.getTime();
      const isPast = cellDate < today;
      const hasMeeting = meetingsByDate.has(cellDateString);

      // Check for Teams/Zoom meetings specifically
      const meetingsOnDate = meetingsByDate.get(cellDateString) || [];
      const hasTeamsMeeting = meetingsOnDate.some(m => m.platform === 'teams');
      const hasZoomMeeting = meetingsOnDate.some(m => m.platform === 'zoom');
      const hasOtherMeeting = meetingsOnDate.some(m => m.platform && m.platform !== 'teams' && m.platform !== 'zoom');

      // Debug: Log meeting detection for today
      if (isToday) {
        console.log('Today\'s date:', cellDateString);
        console.log('Has meeting:', hasMeeting);
        console.log('Meetings on date:', meetingsOnDate);
        console.log('Has Teams meeting:', hasTeamsMeeting);
        console.log('Has Zoom meeting:', hasZoomMeeting);
      }

      // Weekend styling
      const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;

      // Organization holiday styling
      const isOrganizationHoliday = organizationHolidays.includes(cellDateString);

      // Base classes for the day cell
      let cellClasses = `
        relative w-full h-20 rounded-xl transition-all duration-300 
        flex flex-col items-center justify-center cursor-pointer
        focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2
        group hover:scale-[1.02] hover:shadow-lg
      `;

      // Date number styling
      let dateClasses = `
        text-base font-bold transition-all duration-200
        ${isWeekend ? 'text-red-500' : 'text-gray-700'}
        ${isOrganizationHoliday ? 'text-red-600 font-bold' : ''}
      `;

      // Cell background and border styling
      if (isPast) {
        cellClasses += " bg-gray-50/50 hover:bg-gray-100/50 border border-gray-200/50";
        if (hasMeeting) {
          cellClasses += " hover:bg-gradient-to-br hover:from-gray-100 hover:to-gray-200";
        }
      } else {
        if (hasMeeting) {
          cellClasses += " bg-gradient-to-br from-blue-50/50 to-indigo-50/50 border border-blue-200/50 hover:from-blue-100/50 hover:to-indigo-100/50";
        } else {
          cellClasses += " bg-white/80 hover:bg-gradient-to-br hover:from-blue-50/50 hover:to-indigo-50/50 border border-gray-200/50";
        }
      }

      // Selected date styling
      if (isSelected) {
        cellClasses += " bg-gradient-to-br from-blue-600 to-indigo-600 border-2 border-blue-500 shadow-xl shadow-blue-500/30 scale-[1.02]";
        dateClasses += " text-white";
      }

      // Today styling
      if (isToday && !isSelected) {
        cellClasses += " border-2 border-blue-500 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-md";
        dateClasses += " text-blue-700 font-bold";
      }

      days.push(
        <button
          key={day}
          onClick={() => handleDateClick(day)}
          className={cellClasses}
          aria-label={`${day} ${MONTH_NAMES[currentMonth]} ${currentYear}`}
        >
          {/* Date number */}
          <span className={dateClasses}>
            {day}
          </span>

          {/* Meeting indicators */}
          {hasMeeting && (
            <>
              {/* Meeting count badge */}
              <div className={`
                absolute -top-1 -right-1 w-5 h-5 rounded-full 
                flex items-center justify-center text-xs font-bold
                shadow-lg border-2 border-white
                ${isSelected
                  ? "bg-white text-blue-600"
                  : isPast
                    ? "bg-gray-500 text-white"
                    : "bg-gradient-to-r from-red-500 to-pink-500 text-white"
                }
              `}>
                {meetingsOnDate.length}
                {/* Glow effect */}
                <div className={`
                  absolute inset-0 rounded-full blur-sm
                  ${isSelected ? "bg-white/30" : isPast ? "bg-gray-400/50" : "bg-red-400/50"}
                `}></div>
              </div>

              {/* Meeting type indicators */}
              <div className="absolute bottom-1 left-1/2 -translate-x-1/2 flex gap-1">
                {hasTeamsMeeting && (
                  <div className="relative">
                    <span className={`
                      w-2.5 h-2.5 rounded-full shadow-lg
                      ${isSelected
                        ? "bg-white shadow-white/50"
                        : isPast
                          ? "bg-gradient-to-r from-gray-400 to-gray-600 shadow-gray-400/50"
                          : "bg-gradient-to-r from-blue-400 to-blue-600 shadow-blue-400/50"
                      }
                    `}></span>
                    {/* Glow effect */}
                    <div className={`
                      absolute inset-0 w-2.5 h-2.5 rounded-full blur-sm
                      ${isSelected ? "bg-white/40" : isPast ? "bg-gray-400/40" : "bg-blue-400/40"}
                    `}></div>
                  </div>
                )}
                {hasZoomMeeting && (
                  <div className="relative">
                    <span className={`
                      w-2.5 h-2.5 rounded-full shadow-lg
                      ${isSelected
                        ? "bg-white shadow-white/50"
                        : isPast
                          ? "bg-gradient-to-r from-gray-400 to-gray-600 shadow-gray-400/50"
                          : "bg-gradient-to-r from-purple-400 to-purple-600 shadow-purple-400/50"
                      }
                    `}></span>
                    {/* Glow effect */}
                    <div className={`
                      absolute inset-0 w-2.5 h-2.5 rounded-full blur-sm
                      ${isSelected ? "bg-white/40" : isPast ? "bg-gray-400/40" : "bg-purple-400/50"}
                    `}></div>
                  </div>
                )}
                {hasOtherMeeting && (
                  <div className="relative">
                    <span className={`
                      w-2.5 h-2.5 rounded-full shadow-lg
                      ${isSelected
                        ? "bg-white shadow-white/50"
                        : isPast
                          ? "bg-gradient-to-r from-gray-400 to-gray-600 shadow-gray-400/50"
                          : "bg-gradient-to-r from-amber-400 to-amber-600 shadow-amber-400/50"
                      }
                    `}></span>
                    {/* Glow effect */}
                    <div className={`
                      absolute inset-0 w-2.5 h-2.5 rounded-full blur-sm
                      ${isSelected ? "bg-white/40" : isPast ? "bg-gray-400/40" : "bg-amber-400/40"}
                    `}></div>
                  </div>
                )}
              </div>

              {/* Subtle background pattern for meetings */}
              <div className={`
                absolute inset-0 rounded-xl pointer-events-none opacity-20
                ${isSelected
                  ? "bg-gradient-to-br from-white/20 to-white/10"
                  : isPast
                    ? "bg-gradient-to-br from-gray-400/20 to-gray-500/20"
                    : "bg-gradient-to-br from-blue-400/20 to-indigo-500/20"
                }
              `}></div>
            </>
          )}

          {/* Hover effect overlay */}
          <div className="absolute inset-0 rounded-xl bg-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </button>
      );
    }
    return days;
  };

  const renderPlatformSelector = () => (
    <div className="text-center space-y-6">
      <div className="space-y-2">
        <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-2xl flex items-center justify-center">
          <Calendar className="w-8 h-8 text-blue-600" />
        </div>
        <h3 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
          Schedule a Meeting
        </h3>

      </div>

      <div className="space-y-4">
        {([
          {
            platform: 'teams',
            logo: teamsLogoPath,
            name: 'Microsoft Teams',
            bg: 'from-blue-500 to-blue-600',
            hoverBg: 'from-blue-600 to-blue-700',
            icon: '💼',

          },
          {
            platform: 'zoom',
            logo: zoomLogoPath,
            name: 'Zoom Meeting',
            bg: 'from-purple-500 to-purple-600',
            hoverBg: 'from-purple-600 to-purple-700',
            icon: '📹',

          },
          {
            platform: 'inperson',
            logo: '',
            name: 'In-Person Meeting',
            bg: 'from-orange-500 to-orange-600',
            hoverBg: 'from-orange-600 to-orange-700',
            icon: '🤝',

          },
          {
            platform: 'call',
            logo: callLogoPath,
            name: 'Phone Call',
            bg: 'from-green-500 to-green-600',
            hoverBg: 'from-green-600 to-green-700',
            icon: '📞',

          }
        ] as const).map(({ platform, name, bg, hoverBg, icon, }) => (
          <div
            key={platform}
            onClick={() => handlePlatformSelect(platform)}
            className="cursor-pointer group"
          >
            <div className={`
              relative overflow-hidden rounded-2xl p-4 transition-all duration-300 
              bg-gradient-to-r ${bg} hover:${hoverBg} hover:shadow-2xl hover:scale-105
              border border-white/20 shadow-xl
            `}>
              <div className="flex items-center gap-4">
                <div className="flex-shrink-0">
                  <div className="w-5 h-5 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm">
                    <span className="text-2xl">{icon}</span>
                  </div>
                </div>
                <div className="flex-1 text-left">
                  <h4 className="text-white font-bold text-lg mb-1">{name}</h4>
                  <p className="text-white/90 text-sm">{description}</p>
                </div>
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center group-hover:bg-white/30 transition-all duration-300 group-hover:scale-110">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Enhanced hover effect overlay */}
              <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-all duration-300"></div>

              {/* Subtle shine effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderAttendeeInput = (
    type: 'required' | 'optional'
  ) => {
    const selected = type === 'required' ? attendees : attendeesOptional;
    const searchVal = type === 'required' ? attendeeSearch : optionalAttendeeSearch;
    const setSearchVal = type === 'required' ? setAttendeeSearch : setOptionalAttendeeSearch;
    const inputRef = type === 'required' ? attendeeInputRef : optionalAttendeeInputRef;
    const label = type === 'required' ? 'Attendees *' : 'Attendees (Optional)';

    const filteredSuggestions = allAvailableEmails.filter(email =>
      email.toLowerCase().includes(searchVal.toLowerCase()) &&
      !attendees.includes(email) &&
      !attendeesOptional.includes(email)
    );

    return (
      <div className="relative">
        <label className="block text-sm font-medium text-gray-700 mb-1">{label}</label>
        <div
          className="w-full p-2 border border-gray-300 rounded-md flex flex-wrap items-center gap-2"
          onClick={() => inputRef.current?.focus()}
        >
          {selected.map(email => (
            <span key={email} className="bg-blue-500 text-white text-xs font-semibold px-2.5 py-1 rounded-full flex items-center gap-1.5">
              {email}
              <X size={14} className="cursor-pointer" onClick={() => handleAttendeeAction(email, type, 'remove')} />
            </span>
          ))}
          <input
            ref={inputRef}
            type="text"
            value={searchVal}
            onChange={(e) => setSearchVal(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && searchVal) {
                e.preventDefault();
                handleAttendeeAction(searchVal, type, 'add');
              }
            }}
            className="flex-grow bg-transparent outline-none text-sm p-1"
            placeholder="Search or add email"
          />
        </div>
        {searchVal && filteredSuggestions.length > 0 && (
          <div className="absolute z-10 w-full bg-white border border-gray-300 rounded-md mt-1 max-h-40 overflow-y-auto shadow-lg">
            {filteredSuggestions.map(email => (
              <div key={email}
                onClick={() => handleAttendeeAction(email, type, 'add')}
                className="p-2 text-sm hover:bg-blue-50 cursor-pointer">
                {email}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  const renderMeetingForm = () => {
    const canSave = title && selectedStartTime && selectedEndTime && attendees.length > 0;
    return (
      <div className="flex flex-col space-y-6 p-2">
        <div className="flex items-center gap-3 mb-4">
          <button
            onClick={handleBackToPlatformSelection}
            className="p-2 rounded-xl hover:bg-gray-100 transition-colors duration-200 group"
          >
            <ChevronLeft className="w-5 h-5 text-gray-600 group-hover:text-gray-800" />
          </button>
          <div className="flex-1">
            <h3 className="text-lg font-bold text-gray-800">
              New {meetingPlatform.charAt(0).toUpperCase() + meetingPlatform.slice(1)} Meeting
            </h3>
            <p className="text-sm text-gray-500">Fill in the meeting details below</p>
          </div>
        </div>

        <div className="space-y-5">
          <div>
            <label htmlFor="title" className="block text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Meeting Title *
            </label>
            <input
              type="text"
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter meeting title"
              className="w-full p-3 border border-gray-300 rounded-xl text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
            />
          </div>

          {renderAttendeeInput('required')}
          {renderAttendeeInput('optional')}

          <div>
            <label htmlFor="description" className="block text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Description
            </label>
            <textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
              placeholder="Enter meeting description..."
              className="w-full p-3 border border-gray-300 rounded-xl text-sm resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
            ></textarea>
          </div>

          {/* File Attachments */}
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
              <Paperclip className="w-4 h-4" />
              Attachments
            </label>
            <label htmlFor="file-upload" className="w-full p-4 border-2 border-dashed border-gray-300 rounded-xl text-gray-500 cursor-pointer flex items-center gap-3 hover:border-blue-400 hover:bg-blue-50/50 transition-all duration-200 group">
              <Paperclip className="w-5 h-5 text-gray-400 group-hover:text-blue-500" />
              <span className="text-sm">Click to add files</span>
            </label>
            <input id="file-upload" type="file" multiple onChange={handleFileChange} className="hidden" />
            {files.length > 0 && (
              <div className="mt-3 space-y-2">
                {files.map((file, index) => (
                  <div key={index} className="flex justify-between items-center bg-gray-50 p-3 rounded-lg text-sm border border-gray-200">
                    <span className="truncate pr-2 text-gray-700">{file.name}</span>
                    <button
                      onClick={() => handleRemoveFile(index)}
                      className="text-red-500 hover:text-red-700 p-1 rounded-md hover:bg-red-50 transition-colors duration-200"
                    >
                      <X size={16} />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Time and Timezone */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label htmlFor="start-time" className="block text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
                <Clock className="w-4 h-4" />
                Start Time *
              </label>
              <select
                id="start-time"
                value={selectedStartTime}
                onChange={(e) => { setSelectedStartTime(e.target.value); setSelectedEndTime('') }}
                disabled={!selectedDate || availableStartTimes.length === 0}
                className="w-full p-3 border border-gray-300 rounded-xl text-sm bg-white disabled:bg-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
              >
                <option value="">{availableStartTimes.length === 0 ? "No times available" : "Select Start Time"}</option>
                {availableStartTimes.map(t => <option key={t} value={t}>{t}</option>)}
              </select>
            </div>
            <div>
              <label htmlFor="end-time" className="block text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
                <Clock className="w-4 h-4" />
                End Time *
              </label>
              <select
                id="end-time"
                value={selectedEndTime}
                onChange={(e) => setSelectedEndTime(e.target.value)}
                disabled={!selectedStartTime || availableEndTimes.length === 0}
                className="w-full p-3 border border-gray-300 rounded-xl text-sm bg-white disabled:bg-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
              >
                <option value="">{availableEndTimes.length === 0 ? "No times available" : "Select End Time"}</option>
                {availableEndTimes.map(t => <option key={t} value={t}>{t}</option>)}
              </select>
            </div>
          </div>

          <div>
            <label htmlFor="timezone" className="block text-sm font-semibold text-gray-700 mb-2 flex items-center gap-2">
              <Clock className="w-4 h-4" />
              Time Zone
            </label>
            <select
              id="timezone"
              value={timezone}
              onChange={(e) => setTimezone(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-xl text-sm bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
            >
              {timezones.map(tz => <option key={tz.value} value={tz.value}>{tz.label}</option>)}
            </select>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-6 border-t border-gray-200 mt-4">
          <button
            onClick={handleClose}
            className="px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-800 font-semibold rounded-xl transition-all duration-200 hover:shadow-md"
          >
            Cancel
          </button>
          <button
            onClick={handleSaveMeeting}
            disabled={!canSave || waitForSubmission}
            className="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold rounded-xl flex items-center justify-center min-w-[160px] disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover:shadow-lg hover:scale-105"
          >
            {waitForSubmission ? (
              <div className="flex items-center gap-2">
                <ThreeDots height="20" width="20" color="white" />
                <span>Scheduling...</span>
              </div>
            ) : (
              "Schedule Meeting"
            )}
          </button>
        </div>
      </div>
    )
  };

  const meetingsForSelectedDate = selectedDate ? meetingsByDate.get(formatDateToYYYYMMDD(selectedDate)) || [] : [];

  return (
    <div className="w-full h-full overflow-auto flex flex-col font-sans bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <ToastContainer position="top-right" autoClose={3000} hideProgressBar={false} />
      <div className="p-4 sm:p-6 w-full flex-1">
        <div className="grid grid-cols-1 xl:grid-cols-6 gap-6">

          {/* --- Column 1: Calendar & Meetings List --- */}
          <div className="flex flex-col space-y-6 xl:col-span-4">
            {/* Calendar Header */}
            <div className="bg-white/90 backdrop-blur-sm border border-white/20 rounded-3xl p-6 shadow-2xl shadow-blue-500/10 relative overflow-hidden">
              {/* Enhanced background pattern */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50/20 via-transparent to-indigo-50/20 pointer-events-none"></div>
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-400 via-indigo-500 to-purple-500"></div>

              {/* Calendar Navigation */}
              <div className="flex items-center justify-between mb-8">
                <button
                  onClick={handlePrevMonth}
                  className="p-3 rounded-2xl hover:bg-blue-50 hover:shadow-lg transition-all duration-200 group"
                >
                  <ChevronLeft className="w-6 h-6 text-blue-600 group-hover:text-blue-700" />
                </button>

                <div className="text-center">
                  <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2">
                    {MONTH_NAMES[currentMonth]} {currentYear}
                  </h2>
                  <p className="text-sm text-gray-500">Select a date to view or schedule meetings</p>
                </div>

                <div className="flex items-center gap-3">
                  <button
                    onClick={() => {
                      const recruiterId = localStorage.getItem('userId') || '';
                      ApiService.fetchAllMeetings(recruiterId)
                        .then(response => {
                          setBackendMeetings(response.meetings || []);
                          toast.success('Meetings refreshed successfully!');
                        })
                        .catch(error => {
                          console.error('Failed to refresh meetings:', error);
                          toast.error('Failed to refresh meetings');
                        });
                    }}
                    disabled={loadingMeetings}
                    className="p-3 rounded-2xl hover:bg-blue-50 hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed group"
                    title="Refresh meetings"
                  >
                    <RefreshCw className={`w-5 h-5 text-blue-600 ${loadingMeetings ? 'animate-spin' : 'group-hover:rotate-180'} transition-all duration-500`} />
                  </button>

                  <button
                    onClick={handleNextMonth}
                    className="p-3 rounded-2xl hover:bg-blue-50 hover:shadow-lg transition-all duration-200 group"
                  >
                    <ChevronRight className="w-6 h-6 text-blue-600 group-hover:text-blue-700" />
                  </button>
                </div>
              </div>

              {/* Day Headers */}
              <div className="grid grid-cols-7 gap-3 mb-4">
                {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, index) => (
                  <div
                    key={day}
                    className={`py-3 px-2 text-center text-sm font-bold rounded-xl ${index === 0 || index === 6
                      ? 'text-red-500 bg-red-50/50'
                      : 'text-blue-700 bg-blue-50/50'
                      }`}
                  >
                    {day}
                  </div>
                ))}
              </div>

              {/* Calendar Grid */}
              <div className="grid grid-cols-7 gap-3">
                {renderCalendarDays()}
              </div>
            </div>

            {/* Meetings List */}
            <div className="bg-white/90 backdrop-blur-sm border border-white/20 rounded-3xl p-6 shadow-2xl shadow-blue-500/10">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                  <Calendar className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-800 flex items-center gap-2">
                    {selectedDate && selectedDate < today ? 'Past ' : ''}Scheduled Meetings
                  </h3>
                  <p className="text-sm text-gray-500">
                    {selectedDate ? selectedDate.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' }) : 'Select a date to view meetings'}
                  </p>
                </div>
              </div>

              <div className="min-h-[60px] max-h-96 overflow-y-auto space-y-4">
                {!selectedDate ? (
                  <div className="text-center py-12">
                    <div className="w-20 h-20 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                      <Calendar className="w-10 h-10 text-gray-400" />
                    </div>
                    <p className="text-gray-500 font-medium">Select a date to see meetings</p>
                  </div>
                ) : meetingsForSelectedDate.length > 0 ? (
                  meetingsForSelectedDate.map((meeting, index) => (
                    <div
                      key={meeting.id}
                      className={`p-5 border border-gray-200 rounded-2xl transition-all duration-300 cursor-pointer ${meeting.platform ? getMeetingBorderColor(meeting.platform) : 'border-l-4 border-gray-400'
                        } ${meeting.platform ? getMeetingBackgroundColor(meeting.platform) : 'bg-gray-50/50'
                        } ${meeting.joinUrl && (meeting.platform === 'teams' || meeting.platform === 'zoom')
                          ? 'hover:shadow-xl hover:scale-[1.02] hover:border-blue-300'
                          : 'hover:shadow-lg hover:scale-[1.01] hover:border-gray-300'
                        }`}
                      onClick={() => {
                        console.log('Meeting clicked:', meeting);
                        console.log('Join URL:', meeting.joinUrl);
                        console.log('Platform:', meeting.platform);
                        if (meeting.joinUrl && (meeting.platform === 'teams' || meeting.platform === 'zoom')) {
                          console.log('Opening join URL:', meeting.joinUrl);
                          handleJoinMeeting(meeting.joinUrl);
                        } else {
                          console.log('No join URL or invalid platform for meeting:', meeting);
                          toast.warn('This meeting cannot be joined online');
                        }
                      }}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-3 mb-3">
                            <div className="flex-shrink-0">
                              <div className="bg-white/80 backdrop-blur-sm rounded-xl px-4 py-2 border border-gray-200 shadow-sm">
                                <div className="flex items-center gap-2">
                                  <Clock className="w-4 h-4 text-gray-600" />
                                  <span className="font-bold text-gray-800 text-sm">
                                    {meeting.startTime} - {meeting.endTime}
                                  </span>
                                </div>
                              </div>
                            </div>

                            {meeting.platform && (
                              <span className={`px-4 py-2 text-xs rounded-full font-bold ${meeting.platform === 'teams' ? 'bg-blue-100 text-blue-800 border border-blue-200' :
                                meeting.platform === 'zoom' ? 'bg-purple-100 text-purple-800 border border-purple-200' :
                                  meeting.platform === 'inperson' ? 'bg-orange-100 text-orange-800 border border-orange-200' :
                                    meeting.platform === 'call' ? 'bg-green-100 text-green-800 border border-green-200' :
                                      'bg-gray-100 text-gray-800 border border-gray-200'
                                }`}>
                                {meeting.platform === 'inperson' ? 'In-Person' :
                                  meeting.platform.charAt(0).toUpperCase() + meeting.platform.slice(1)}
                              </span>
                            )}
                          </div>

                          <div className="text-gray-800 font-bold text-lg mb-3 line-clamp-2">
                            {meeting.title}
                            {meeting.joinUrl && (meeting.platform === 'teams' || meeting.platform === 'zoom') && (
                              <span className="text-sm text-blue-600 font-medium ml-3">(Click to join)</span>
                            )}
                          </div>

                          {meeting.description && (
                            <div className="text-sm text-gray-600 mb-3 line-clamp-2">{meeting.description}</div>
                          )}

                          {meeting.attendees && meeting.attendees.length > 0 && (
                            <div className="text-sm text-gray-500 flex items-center gap-2">
                              <Users className="w-4 h-4" />
                              <span className="font-medium">Attendees:</span> {meeting.attendees.slice(0, 3).join(', ')}
                              {meeting.attendees.length > 3 && ` +${meeting.attendees.length - 3} more`}
                            </div>
                          )}
                        </div>

                        {meeting.joinUrl && meeting.platform && (meeting.platform === 'teams' || meeting.platform === 'zoom') && (
                          <div className="flex-shrink-0 ml-4">
                            <div className={`px-6 py-3 text-white text-sm rounded-xl flex items-center gap-2 font-bold shadow-lg transition-all duration-200 hover:scale-105 ${meeting.platform === 'teams' ? 'bg-gradient-to-r from-blue-600 to-blue-700' :
                              meeting.platform === 'zoom' ? 'bg-gradient-to-r from-purple-600 to-purple-700' :
                                'bg-gradient-to-r from-green-600 to-green-700'
                              }`}>
                              <ExternalLink size={16} />
                              Join
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-12">
                    <div className="w-20 h-20 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                      <Calendar className="w-10 h-10 text-gray-400" />
                    </div>
                    <p className="text-gray-500 font-medium mb-2">
                      {selectedDate && selectedDate < today ? 'No meetings were scheduled for this past date.' : 'No meetings scheduled for this date.'}
                    </p>
                    <p className="text-gray-400 text-sm">
                      {selectedDate && selectedDate < today ? 'This date is in the past.' : 'Click on a platform below to schedule one!'}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* --- Column 2: Form Area --- */}
          <div className="flex flex-col space-y-6 bg-white/90 backdrop-blur-sm border border-white/20 rounded-3xl p-6 shadow-2xl shadow-blue-500/10 min-h-[400px] xl:col-span-2">
            {!selectedDate ? (
              <div className="flex items-center justify-center h-full text-center">
                <div className="space-y-3">
                  <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center">
                    <Calendar className="w-8 h-8 text-gray-400" />
                  </div>
                  <p className="text-gray-500 font-medium">Select a date to schedule a new meeting</p>
                </div>
              </div>
            ) : selectedDate < today ? (
              <div className="flex flex-col items-center justify-center h-full text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                  <Calendar className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-bold text-gray-700 mb-2">Past Date Selected</h3>
                <p className="text-gray-500 text-sm mb-4">You can view meetings from this date, but cannot schedule new ones.</p>
                <div className="text-xs text-gray-400 bg-gray-50 px-4 py-3 rounded-xl border border-gray-200">
                  <span className="font-medium">Selected:</span> {selectedDate.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' })}
                </div>
              </div>
            ) : !showForm ? (
              renderPlatformSelector()
            ) : (
              renderMeetingForm()
            )}
          </div>

        </div>
      </div>

      {/* Schedule Meeting Modal */}
      {showScheduleModal && selectedCandidate && (
        <ScheduleMeetingModal
          candidate={selectedCandidate}
          isOpen={showScheduleModal}
          meetingType={selectedMeetingType}
          onClose={() => {
            setShowScheduleModal(false);
            setSelectedCandidate(null);
            setSelectedMeetingType(undefined);
          }}
        />
      )}

      {/* In-Person Meeting Modal */}
      {showInPersonModal && selectedCandidate && (
        <InPersonMeetingModal
          candidate={selectedCandidate}
          isOpen={showInPersonModal}
          selectedDate={selectedDate}
          onClose={() => {
            setShowInPersonModal(false);
            setSelectedCandidate(null);
          }}
        />
      )}

      {/* Call Meeting Modal */}
      {showCallModal && selectedCandidate && (
        <CallMeetingModal
          candidate={selectedCandidate}
          isOpen={showCallModal}
          selectedDate={selectedDate}
          onClose={() => {
            setShowCallModal(false);
            setSelectedCandidate(null);
          }}
        />
      )}
    </div>
  );
};

export default MergedCalendarView;