// Define the Candidate type with required properties
export interface Candidate {
  id: number;
  jobId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  client: string;
  profile: string;
  skills: string;
  status: string;
  appliedDate: string;
  source: string;
  experience: number;
  education: string;
  location: string;
  salary: string;
  notes: string;
  lastUpdated: string;
  comment: string;
  peerReviewer: string;
  recruiter: string;
  management: string | null;
  client_assigned: boolean;
}

// Define column configuration
export interface Column {
  key: keyof Candidate;
  label: string;
  sortable: boolean;
}
