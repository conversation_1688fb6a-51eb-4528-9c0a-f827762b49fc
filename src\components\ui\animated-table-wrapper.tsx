import { ReactNode, useEffect } from "react";
import { motion, AnimatePresence, useAnimation } from "framer-motion";

interface AnimatedTableWrapperProps {
  /**
   * The table component to animate
   */
  children: ReactNode;

  /**
   * Whether the table is in loading state
   */
  isLoading?: boolean;

  /**
   * Loading component to show when isLoading is true
   */
  loadingComponent?: ReactNode;

  /**
   * Additional CSS classes for the wrapper
   */
  className?: string;
}

/**
 * A wrapper component that adds Framer Motion animations to existing tables
 */
export function AnimatedTableWrapper({
  children,
  isLoading = false,
  loadingComponent,
  className = "",
}: AnimatedTableWrapperProps) {
  const controls = useAnimation();

  useEffect(() => {
    if (!isLoading) {
      controls.start({
        opacity: 1,
        y: 0,
        transition: {
          duration: 0.5,
          type: "spring",
          stiffness: 100,
          damping: 15,
        },
      });
    } else {
      controls.start({
        opacity: 0.7,
        y: 10,
        transition: { duration: 0.3 },
      });
    }
  }, [isLoading, controls]);

  return (
    <div className={`relative ${className}`}>
      <AnimatePresence mode="wait">
        {isLoading && (
          <motion.div
            key="loading"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10"
          >
            {loadingComponent || (
              <div className="text-center">
                <svg
                  className="mx-auto h-12 w-12 animate-spin text-blue-600"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                <p className="mt-2 text-sm font-medium text-gray-700">
                  Loading data...
                </p>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      <motion.div animate={controls} initial={{ opacity: 0, y: 20 }}>
        {children}
      </motion.div>
    </div>
  );
}
